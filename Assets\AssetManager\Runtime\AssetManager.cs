using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using Nyxion.AssetManager.Data;
using Nyxion.AssetManager.Materials;
using Nyxion.AssetManager.Network;
using Nyxion.AssetManager.Providers;
using Nyxion.AssetManager.Settings;

namespace Nyxion.AssetManager
{
    /// <summary>
    /// Main entry point for the Asset Manager system
    /// Provides async API for Addressables bootstrap, catalog updates, and asset loading/unloading
    /// </summary>
    public class AssetManager : MonoBehaviour
    {
        private static AssetManager _instance;
        public static AssetManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<AssetManager>();
                    if (_instance == null)
                    {
                        var go = new GameObject("AssetManager");
                        _instance = go.AddComponent<AssetManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        [SerializeField] private RemoteContentSettings _settings;
        
        private MaterialRegistry _materialRegistry;
        private CatalogClient _catalogClient;
        private List<IModelProvider> _modelProviders;
        private CatalogData _currentCatalog;
        private bool _isInitialized = false;

        /// <summary>
        /// Event fired when initialization completes
        /// </summary>
        public event Action<bool> InitializationCompleted;

        /// <summary>
        /// Event fired when catalog is updated
        /// </summary>
        public event Action<CatalogData> CatalogUpdated;

        /// <summary>
        /// Event fired when asset loading progress changes
        /// </summary>
        public event Action<string, float> LoadProgressChanged;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }

            InitializeComponents();
        }

        private void InitializeComponents()
        {
            _materialRegistry = new MaterialRegistry();
            _catalogClient = new CatalogClient();
            
            _modelProviders = new List<IModelProvider>
            {
                new AddressablesModelProvider(),
                new GltfModelProvider()
            };

            // Subscribe to provider events
            foreach (var provider in _modelProviders)
            {
                provider.LoadProgressChanged += OnProviderLoadProgressChanged;
            }
        }

        /// <summary>
        /// Initializes the Asset Manager system
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if initialization succeeded</returns>
        public async Task<bool> InitializeAsync(CancellationToken cancellationToken = default)
        {
            if (_isInitialized)
            {
                Debug.Log("AssetManager already initialized");
                return true;
            }

            try
            {
                Debug.Log("Initializing AssetManager...");

                // Load settings if not assigned
                if (_settings == null)
                {
                    _settings = Resources.Load<RemoteContentSettings>("RemoteContentSettings");
                    if (_settings == null)
                    {
                        Debug.LogWarning("RemoteContentSettings not found in Resources folder. Using default settings.");
                        _settings = ScriptableObject.CreateInstance<RemoteContentSettings>();
                    }
                }

                // Initialize Addressables
                var initHandle = Addressables.InitializeAsync();
                while (!initHandle.IsDone && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Yield();
                }

                cancellationToken.ThrowIfCancellationRequested();

                if (initHandle.Status != AsyncOperationStatus.Succeeded)
                {
                    Debug.LogError($"Failed to initialize Addressables: {initHandle.OperationException?.Message}");
                    InitializationCompleted?.Invoke(false);
                    return false;
                }

                Debug.Log("Addressables initialized successfully");

                // Auto-update catalog if enabled
                if (_settings.AutoUpdateCatalogOnStart)
                {
                    await UpdateRemoteCatalogAsync(cancellationToken);
                }

                _isInitialized = true;
                Debug.Log("AssetManager initialization complete");
                InitializationCompleted?.Invoke(true);
                return true;
            }
            catch (OperationCanceledException)
            {
                Debug.Log("AssetManager initialization cancelled");
                InitializationCompleted?.Invoke(false);
                throw;
            }
            catch (Exception ex)
            {
                Debug.LogError($"AssetManager initialization failed: {ex.Message}");
                InitializationCompleted?.Invoke(false);
                return false;
            }
        }

        /// <summary>
        /// Updates the remote catalog
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if updates were applied</returns>
        public async Task<bool> UpdateRemoteCatalogAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                Debug.Log("Checking for catalog updates...");

                // Check for Addressables catalog updates
                var checkHandle = Addressables.CheckForCatalogUpdates(false);
                while (!checkHandle.IsDone && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Yield();
                }

                cancellationToken.ThrowIfCancellationRequested();

                bool hasUpdates = false;
                if (checkHandle.Status == AsyncOperationStatus.Succeeded && checkHandle.Result.Count > 0)
                {
                    Debug.Log($"Found {checkHandle.Result.Count} catalog updates");
                    
                    var updateHandle = Addressables.UpdateCatalogs(checkHandle.Result, false);
                    while (!updateHandle.IsDone && !cancellationToken.IsCancellationRequested)
                    {
                        await Task.Yield();
                    }

                    if (updateHandle.Status == AsyncOperationStatus.Succeeded)
                    {
                        hasUpdates = true;
                        Debug.Log("Catalog updates applied successfully");
                    }
                    
                    Addressables.Release(updateHandle);
                }
                
                Addressables.Release(checkHandle);

                // Fetch the latest catalog data
                await FetchCatalogAsync(cancellationToken);

                return hasUpdates;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to update remote catalog: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Fetches the catalog data from the remote server
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The fetched catalog data</returns>
        public async Task<CatalogData> FetchCatalogAsync(CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(_settings.CatalogUri))
            {
                Debug.LogWarning("Catalog URI not configured");
                return new CatalogData();
            }

            try
            {
                var catalogData = await _catalogClient.FetchCatalogAsync(_settings.CatalogUri, cancellationToken);
                _currentCatalog = catalogData;
                
                // Update material registry with new material info
                _materialRegistry.UpdateMaterialInfo(catalogData.materials);
                
                CatalogUpdated?.Invoke(catalogData);
                return catalogData;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to fetch catalog: {ex.Message}");
                return _currentCatalog ?? new CatalogData();
            }
        }

        /// <summary>
        /// Gets the current catalog data
        /// </summary>
        /// <returns>Current catalog or null if not loaded</returns>
        public CatalogData GetCurrentCatalog()
        {
            return _currentCatalog;
        }

        /// <summary>
        /// Gets the estimated download size for an asset
        /// </summary>
        /// <param name="addressOrLabel">Addressables address or label</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Size in bytes</returns>
        public async Task<long> GetDownloadSizeAsync(string addressOrLabel, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(addressOrLabel))
                return 0;

            try
            {
                // Try each provider to get download size
                foreach (var provider in _modelProviders)
                {
                    if (provider.CanHandle(addressOrLabel))
                    {
                        return await provider.GetDownloadSizeAsync(addressOrLabel, cancellationToken);
                    }
                }

                // Fallback to Addressables
                var sizeHandle = Addressables.GetDownloadSizeAsync(addressOrLabel);
                while (!sizeHandle.IsDone && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Yield();
                }

                if (sizeHandle.Status == AsyncOperationStatus.Succeeded)
                {
                    var size = sizeHandle.Result;
                    Addressables.Release(sizeHandle);
                    return size;
                }
                
                if (sizeHandle.IsValid())
                    Addressables.Release(sizeHandle);
                
                return 0;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to get download size for {addressOrLabel}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Preloads materials by their keys
        /// </summary>
        /// <param name="materialKeys">Material keys to preload</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Async enumerable of download progress</returns>
        public async IAsyncEnumerable<DownloadProgress> PreloadMaterialsAsync(IEnumerable<string> materialKeys, [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
        {
            var keys = materialKeys.ToList();
            var totalKeys = keys.Count;
            var completedKeys = 0;

            foreach (var key in keys)
            {
                if (cancellationToken.IsCancellationRequested)
                    yield break;

                var progress = new DownloadProgress($"material_{key}", $"Loading material: {key}");
                yield return progress;

                // Load material and handle result
                var result = await LoadMaterialSafelyAsync(key, cancellationToken);
                if (result.Success)
                {
                    completedKeys++;
                    yield return DownloadProgress.Completed($"material_{key}", $"Loaded material: {key}", 0);
                }
                else
                {
                    yield return DownloadProgress.Failed($"material_{key}", $"Failed to load material: {key}", result.ErrorMessage);
                }
            }
        }

        /// <summary>
        /// Safely loads a material and returns result without throwing exceptions
        /// </summary>
        private async Task<(bool Success, string ErrorMessage)> LoadMaterialSafelyAsync(string key, CancellationToken cancellationToken)
        {
            try
            {
                var material = await _materialRegistry.GetMaterialAsync(key, cancellationToken);
                return (material != null, material == null ? "Material not found" : string.Empty);
            }
            catch (Exception ex)
            {
                return (false, ex.Message);
            }
        }

        /// <summary>
        /// Loads a prefab and instantiates it
        /// </summary>
        /// <param name="prefabAddress">Prefab address or URL</param>
        /// <param name="parent">Parent transform</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Instantiated GameObject</returns>
        public async Task<GameObject> LoadPrefabAsync(string prefabAddress, Transform parent = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(prefabAddress))
                return null;

            // Find appropriate provider
            foreach (var provider in _modelProviders)
            {
                if (provider.CanHandle(prefabAddress))
                {
                    return await provider.LoadAsync(prefabAddress, parent, cancellationToken);
                }
            }

            Debug.LogWarning($"No provider found for address: {prefabAddress}");
            return null;
        }

        /// <summary>
        /// Loads a shared material by key
        /// </summary>
        /// <param name="materialKey">Material key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Loaded material</returns>
        public async Task<Material> LoadSharedMaterialAsync(string materialKey, CancellationToken cancellationToken = default)
        {
            return await _materialRegistry.GetMaterialAsync(materialKey, cancellationToken);
        }

        /// <summary>
        /// Applies shared materials to a GameObject based on material slots
        /// </summary>
        /// <param name="instance">GameObject instance</param>
        /// <param name="slots">Material slots to apply</param>
        /// <param name="cancellationToken">Cancellation token</param>
        public async Task ApplySharedMaterialsBySlotsAsync(GameObject instance, IEnumerable<MaterialSlot> slots, CancellationToken cancellationToken = default)
        {
            if (instance == null || slots == null)
                return;

            var renderers = instance.GetComponentsInChildren<Renderer>();

            foreach (var slot in slots)
            {
                if (string.IsNullOrEmpty(slot.slotName) || string.IsNullOrEmpty(slot.materialKey))
                    continue;

                var material = await _materialRegistry.GetMaterialAsync(slot.materialKey, cancellationToken);
                if (material == null)
                {
                    Debug.LogWarning($"Failed to load material {slot.materialKey} for slot {slot.slotName}");
                    continue;
                }

                // Find renderer with matching name or apply to all if slot name is "*"
                foreach (var renderer in renderers)
                {
                    if (slot.slotName == "*" || renderer.name.Contains(slot.slotName))
                    {
                        renderer.material = material;
                        Debug.Log($"Applied material {slot.materialKey} to {renderer.name}");
                    }
                }
            }
        }

        /// <summary>
        /// Releases an asset
        /// </summary>
        /// <param name="handleTarget">Asset to release</param>
        public async Task ReleaseAsync(UnityEngine.Object handleTarget)
        {
            if (handleTarget == null)
                return;

            // Try to release through providers first
            if (handleTarget is GameObject gameObject)
            {
                foreach (var provider in _modelProviders)
                {
                    try
                    {
                        provider.Release(gameObject);
                        return;
                    }
                    catch
                    {
                        // Continue to next provider
                    }
                }
            }

            // Fallback to Addressables release
            try
            {
                Addressables.Release(handleTarget);
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to release asset {handleTarget.name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Clears all cached assets
        /// </summary>
        public async Task ClearCacheAsync()
        {
            _materialRegistry?.ClearCache();

            // Clear provider caches
            foreach (var provider in _modelProviders)
            {
                if (provider is AddressablesModelProvider addressablesProvider)
                {
                    addressablesProvider.ReleaseAll();
                }
                else if (provider is GltfModelProvider gltfProvider)
                {
                    gltfProvider.ReleaseAll();
                }
            }

            // Clear Addressables cache
            try
            {
                var cleanHandle = Addressables.CleanBundleCache();
                await cleanHandle.Task;
                Debug.Log("Asset cache cleared");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to clear Addressables cache: {ex.Message}");
            }
        }

        private void OnProviderLoadProgressChanged(string address, float progress)
        {
            LoadProgressChanged?.Invoke(address, progress);
        }

        private void OnDestroy()
        {
            _materialRegistry?.Dispose();

            if (_modelProviders != null)
            {
                foreach (var provider in _modelProviders)
                {
                    provider.LoadProgressChanged -= OnProviderLoadProgressChanged;
                }
            }
        }
    }
}
