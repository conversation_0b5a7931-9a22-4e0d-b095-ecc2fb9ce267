# Refer to https://internaldocs.unity.com/renovate/ for more documentation

# This workflow runs Renovate against the current repo and will create PRs with outdated dependencies.
name: Renovate

on:
  workflow_dispatch:
    inputs:
      log-level:
        type: choice
        description: Select log level for Renovate
        options:
          - trace
          - debug
          - info
          - warn
          - error
        default: info
        required: false
  schedule:
    # Default daily scheduled run. Feel free to change this to run when you want it to.
    - cron: '25 17 * * *'

jobs:
  renovate:
    # The reusable workflow will be updated by renovate if there's a new version
    uses: unity/renovate-workflows/.github/workflows/run.yml@v4.0.0
    with:
      # This is the image that contains our custom renovate and will be auto
      # updated by Renovate itself.
      image: europe-docker.pkg.dev/unity-cds-services-prd/ds-docker/renovate:10.2.4@sha256:3c19b168b1a9a4ca076ad5f858176ef0b2eafc34aafcd6e2276133a563d35112
      log-level: ${{ github.event.inputs.log-level }}
    secrets:
      renovate-auth-secret: ${{ secrets.RENOVATE_AUTH_SECRET }}
      github-com-token: ${{ secrets.GH_COM_TOKEN }}


