using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using Nyxion.AssetManager.Data;

namespace Nyxion.AssetManager.Materials
{
    /// <summary>
    /// Centralized registry for managing shared materials with Addressables
    /// </summary>
    public class MaterialRegistry : IDisposable
    {
        private readonly Dictionary<string, Material> _materialCache = new Dictionary<string, Material>();
        private readonly Dictionary<string, AsyncOperationHandle<Material>> _loadingOperations = new Dictionary<string, AsyncOperationHandle<Material>>();
        private readonly Dictionary<string, MaterialInfo> _materialInfoCache = new Dictionary<string, MaterialInfo>();
        private readonly object _lock = new object();
        private bool _disposed = false;

        /// <summary>
        /// Event fired when a material is loaded
        /// </summary>
        public event Action<string, Material> MaterialLoaded;

        /// <summary>
        /// Event fired when a material fails to load
        /// </summary>
        public event Action<string, string> MaterialLoadFailed;

        /// <summary>
        /// Updates the material info cache with new catalog data
        /// </summary>
        /// <param name="materials">List of material info from catalog</param>
        public void UpdateMaterialInfo(IEnumerable<MaterialInfo> materials)
        {
            lock (_lock)
            {
                _materialInfoCache.Clear();
                foreach (var materialInfo in materials)
                {
                    if (!string.IsNullOrEmpty(materialInfo.key))
                    {
                        _materialInfoCache[materialInfo.key] = materialInfo;
                    }
                }
            }
            
            Debug.Log($"Updated material info cache with {_materialInfoCache.Count} materials");
        }

        /// <summary>
        /// Gets a material by key, loading it if necessary
        /// </summary>
        /// <param name="materialKey">Material key (e.g., "Wood_Oak_01")</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The loaded material or null if not found</returns>
        public async Task<Material> GetMaterialAsync(string materialKey, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(materialKey))
            {
                Debug.LogWarning("Material key is null or empty");
                return null;
            }

            // Check cache first
            lock (_lock)
            {
                if (_materialCache.TryGetValue(materialKey, out var cachedMaterial))
                {
                    return cachedMaterial;
                }
            }

            // Load the material
            return await LoadMaterialAsync(materialKey, cancellationToken);
        }

        /// <summary>
        /// Preloads multiple materials
        /// </summary>
        /// <param name="materialKeys">Material keys to preload</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary of loaded materials by key</returns>
        public async Task<Dictionary<string, Material>> PreloadMaterialsAsync(IEnumerable<string> materialKeys, CancellationToken cancellationToken = default)
        {
            var results = new Dictionary<string, Material>();
            var tasks = new List<Task>();

            foreach (var key in materialKeys)
            {
                if (string.IsNullOrEmpty(key)) continue;

                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        var material = await GetMaterialAsync(key, cancellationToken);
                        if (material != null)
                        {
                            lock (results)
                            {
                                results[key] = material;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Failed to preload material {key}: {ex.Message}");
                    }
                }, cancellationToken));
            }

            await Task.WhenAll(tasks);
            return results;
        }

        /// <summary>
        /// Checks if a material is cached
        /// </summary>
        /// <param name="materialKey">Material key to check</param>
        /// <returns>True if material is in cache</returns>
        public bool IsMaterialCached(string materialKey)
        {
            if (string.IsNullOrEmpty(materialKey)) return false;
            
            lock (_lock)
            {
                return _materialCache.ContainsKey(materialKey);
            }
        }

        /// <summary>
        /// Gets material info for a given key
        /// </summary>
        /// <param name="materialKey">Material key</param>
        /// <returns>Material info or null if not found</returns>
        public MaterialInfo GetMaterialInfo(string materialKey)
        {
            if (string.IsNullOrEmpty(materialKey)) return null;
            
            lock (_lock)
            {
                return _materialInfoCache.TryGetValue(materialKey, out var info) ? info : null;
            }
        }

        /// <summary>
        /// Loads a material using Addressables
        /// </summary>
        private async Task<Material> LoadMaterialAsync(string materialKey, CancellationToken cancellationToken)
        {
            var addressableKey = $"mat:{materialKey}";
            
            try
            {
                // Check if already loading
                AsyncOperationHandle<Material> existingOp = default;
                bool hasExistingOp = false;

                lock (_lock)
                {
                    if (_loadingOperations.TryGetValue(materialKey, out existingOp))
                    {
                        hasExistingOp = true;
                    }
                }

                if (hasExistingOp)
                {
                    // Wait for existing operation outside the lock
                    while (!existingOp.IsDone && !cancellationToken.IsCancellationRequested)
                    {
                        await Task.Yield();
                    }

                    if (existingOp.Status == AsyncOperationStatus.Succeeded)
                    {
                        return existingOp.Result;
                    }
                }

                Debug.Log($"Loading material: {addressableKey}");
                var handle = Addressables.LoadAssetAsync<Material>(addressableKey);
                
                lock (_lock)
                {
                    _loadingOperations[materialKey] = handle;
                }

                // Wait for completion
                while (!handle.IsDone && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Yield();
                }

                cancellationToken.ThrowIfCancellationRequested();

                if (handle.Status == AsyncOperationStatus.Succeeded)
                {
                    var material = handle.Result;
                    
                    lock (_lock)
                    {
                        _materialCache[materialKey] = material;
                        _loadingOperations.Remove(materialKey);
                    }
                    
                    MaterialLoaded?.Invoke(materialKey, material);
                    Debug.Log($"Successfully loaded material: {materialKey}");
                    return material;
                }
                else
                {
                    var errorMessage = $"Failed to load material {addressableKey}: {handle.OperationException?.Message ?? "Unknown error"}";
                    Debug.LogError(errorMessage);
                    
                    lock (_lock)
                    {
                        _loadingOperations.Remove(materialKey);
                    }
                    
                    MaterialLoadFailed?.Invoke(materialKey, errorMessage);
                    Addressables.Release(handle);
                    return null;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Exception loading material {materialKey}: {ex.Message}";
                Debug.LogError(errorMessage);
                MaterialLoadFailed?.Invoke(materialKey, errorMessage);
                return null;
            }
        }

        /// <summary>
        /// Releases a material from cache
        /// </summary>
        /// <param name="materialKey">Material key to release</param>
        public void ReleaseMaterial(string materialKey)
        {
            if (string.IsNullOrEmpty(materialKey)) return;

            lock (_lock)
            {
                if (_materialCache.TryGetValue(materialKey, out var material))
                {
                    _materialCache.Remove(materialKey);
                    
                    // Release the Addressables handle
                    var addressableKey = $"mat:{materialKey}";
                    Addressables.Release(material);
                    
                    Debug.Log($"Released material: {materialKey}");
                }
            }
        }

        /// <summary>
        /// Clears all cached materials
        /// </summary>
        public void ClearCache()
        {
            lock (_lock)
            {
                foreach (var kvp in _materialCache)
                {
                    Addressables.Release(kvp.Value);
                }
                
                _materialCache.Clear();
                _materialInfoCache.Clear();
                
                // Cancel any ongoing operations
                foreach (var kvp in _loadingOperations)
                {
                    if (kvp.Value.IsValid())
                    {
                        Addressables.Release(kvp.Value);
                    }
                }
                _loadingOperations.Clear();
            }
            
            Debug.Log("Material cache cleared");
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                ClearCache();
                _disposed = true;
            }
        }
    }
}
