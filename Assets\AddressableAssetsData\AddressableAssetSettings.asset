%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: cdadd8bc4da9e5d4ea98fb3c88a03a86
  m_currentHash:
    serializedVersion: 2
    Hash: 4dc4aad188437b7b9b8500d3d748f527
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 1
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider
  m_AssetBundleProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_EnableJsonCatalog: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 86cc8921cef9e2d4e886a79ad0b9996e
  m_RemoteCatalogLoadPath:
    m_Id: 510c37c788d15cf4dac1baf9066b83e8
  m_ContentStateBuildPathProfileVariableName: <default settings path>
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: 3332dae7d9c02354f971a6228a60dec6, type: 2}
  - {fileID: 11400000, guid: e92ac049fe1e26a499be5f3f45926d65, type: 2}
  - {fileID: 11400000, guid: e4e41cf7f98837448b3cb6e7c636d9d0, type: 2}
  - {fileID: 11400000, guid: 74e32e3fd2a420c48a63367582a748c6, type: 2}
  - {fileID: 11400000, guid: a5d5854e430a3564483151eace8e5dae, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: 0a98f1ec5dbd108468bed1743a78262e
      m_ProfileName: Default
      m_Values:
      - m_Id: 0d70c9c1916b24d48afdef49789894e3
        m_Value: ServerData/StandaloneWindows64
      - m_Id: 2aa8e19b446980741a5cab8439c7c7e3
        m_Value: 'ServerData/[BuildTarget]'
      - m_Id: 510c37c788d15cf4dac1baf9066b83e8
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: 6e802af10b135d84e8bf1a71f0e15cfc
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 86cc8921cef9e2d4e886a79ad0b9996e
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: e4bcae2dde786a84d8ac1163bde00565
        m_Value: 'http://localhost:5000/assets/[BuildTarget]'
      - m_Id: eda2dff0d7803624297660448e7e5832
        m_Value: http://localhost:5000/assets/StandaloneWindows64
    m_ProfileEntryNames:
    - m_Id: 0d70c9c1916b24d48afdef49789894e3
      m_Name: RemoteBuildPath
      m_InlineUsage: 0
    - m_Id: 2aa8e19b446980741a5cab8439c7c7e3
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: 510c37c788d15cf4dac1baf9066b83e8
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: 6e802af10b135d84e8bf1a71f0e15cfc
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: 86cc8921cef9e2d4e886a79ad0b9996e
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: e4bcae2dde786a84d8ac1163bde00565
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    - m_Id: eda2dff0d7803624297660448e7e5832
      m_Name: RemoteLoadPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 31db54a972190c241bee472a262b8e7f, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: c380875ea0608c54ab712ce991e4529f, type: 2}
  - {fileID: 11400000, guid: 69a8d5ed8d7c8204d9a167d28f91fa4d, type: 2}
  - {fileID: 11400000, guid: d785724f685cf4a44ac0666b350ccdc2, type: 2}
  m_ActiveProfileId: 0a98f1ec5dbd108468bed1743a78262e
