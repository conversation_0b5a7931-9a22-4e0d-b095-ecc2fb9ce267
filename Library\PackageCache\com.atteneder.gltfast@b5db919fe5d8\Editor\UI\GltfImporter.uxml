<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements">
    <ui:VisualElement name="Header" class="header section" style="align-items: center;">
        <Style src="GltfImporter-style.uss" />
        <ui:VisualElement name="gltf-logo" style="background-image: url(&apos;/Packages/com.atteneder.gltfast/Editor/UI/gltf-logo.png&apos;); width: 48px; height: 24px; -unity-background-image-tint-color: rgba(210, 210, 210, 255);" />
        <ui:Label text="Settings" name="Headline" class="h1" style="margin: 0;"/>
    </ui:VisualElement>
    <ui:VisualElement name="ImportSettings" class="section">
        <Style src="GltfImporter-style.uss" />
        <uie:EnumField label="Animation" value="Legacy" binding-path="importSettings.animationMethod" type="GLTFast.AnimationMethod, glTFast" name="Animation" focusable="true" tooltip="Target animation system" />
        <ui:Toggle label="Generate Lightmap UVs" name="SecondaryUVSet" binding-path="editorImportSettings.generateSecondaryUVSet" text="&#x9;" tooltip="Generate lightmap texture coordinates into the second UV channel (if not present)" />
    </ui:VisualElement>
    <ui:VisualElement name="AdvancedSettings" class="section">
        <Style src="GltfImporter-style.uss" />
    </ui:VisualElement>
    <ui:VisualElement name="Dependencies" class="dependencies section">
        <Style src="GltfImporter-style.uss" />
        <ui:VisualElement name="DepsHeader" style="flex-direction: row; align-items: center;">
            <ui:VisualElement name="Icon" class="icon error" />
            <ui:Label text="Some dependencies couldn&apos;t be resolved&#10;" style="-unity-font-style: bold;" />
        </ui:VisualElement>
        <ui:Foldout text="Show Corrupt Dependencies" value="false" name="DepsFoldout" class="deps-foldout" />
        <ui:Button name="fixall" text="Fix All Errors" />
    </ui:VisualElement>
    <ui:VisualElement name="Report" class="report section">
        <Style src="GltfImporter-style.uss" />
        <ui:Label text="Report" name="Headline" class="h1" />
        <ui:ListView focusable="true" binding-path="reportItems" virtualization-method="DynamicHeight" show-bound-collection-size="false" class="report-list" />
    </ui:VisualElement>
</ui:UXML>
