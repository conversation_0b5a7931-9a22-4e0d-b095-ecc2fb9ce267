Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
C:/Users/<USER>/OneDrive/Documents/Nyxion/House_VR
-logFile
Logs/AssetImportWorker4.log
-srvPort
57680
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/OneDrive/Documents/Nyxion/House_VR
C:/Users/<USER>/OneDrive/Documents/Nyxion/House_VR
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13936]  Target information:

Player connection [13936]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 539911160 [EditorId] 539911160 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13936] Host joined multi-casting on [***********:54997]...
Player connection [13936] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2560.28 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/OneDrive/Documents/Nyxion/House_VR/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56444
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 1.507875 seconds.
- Loaded All Assemblies, in 27.743 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 374 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 28423ms
	BeginReloadAssembly (20529ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (1226ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (1546ms)
	LoadAllAssembliesAndSetupDomain (4431ms)
		LoadAssemblies (20536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (4419ms)
			TypeCache.Refresh (4417ms)
				TypeCache.ScanAssembly (3504ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (648ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (466ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (84ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 29.238 seconds
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.02 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 30191ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (29026ms)
		LoadAssemblies (27743ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1373ms)
			TypeCache.Refresh (1318ms)
				TypeCache.ScanAssembly (1303ms)
			BuildScriptInfoCaches (42ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (956ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (830ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (585ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 8.36 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 194 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5804 unused Assets / (9.6 MB). Loaded Objects now: 6391.
Memory consumption went from 166.8 MB to 157.2 MB.
Total: 8.448500 ms (FindLiveObjects: 0.578000 ms CreateObjectMapping: 0.361600 ms MarkObjects: 3.944300 ms  DeleteObjects: 3.563200 ms)

========================================================================
Received Import Request.
  Time since last request: 418402.905606 seconds.
  path: Assets/Samples
  artifactKey: Guid(f17e771607346bb4ea2d7cdc50a90cbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples using Guid(f17e771607346bb4ea2d7cdc50a90cbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2eaa106db50f2a8392324391c5dd3128') in 0.0338014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.121179 seconds.
  path: Assets/Samples/XR Interaction Toolkit
  artifactKey: Guid(3099165e1531cb747bf5f107ed487035) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit using Guid(3099165e1531cb747bf5f107ed487035) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '64786c7d48a912f3a267f80d15e90932') in 0.0044107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.675477 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.1.1
  artifactKey: Guid(8523733eab10ce64e8b05f91952fcd83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.1.1 using Guid(8523733eab10ce64e8b05f91952fcd83) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd3d37324d3b854be5a6cd86793b6d03') in 0.0004951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.993377 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets
  artifactKey: Guid(bdb10265ae57b1642902507966c6b4af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets using Guid(bdb10265ae57b1642902507966c6b4af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '601e7322170834559fc0e69c1b1e94e4') in 0.0005341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.099928 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets/StarterAssets.asmdef
  artifactKey: Guid(8f07e33567e0ee542b40769c456c6b53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets/StarterAssets.asmdef using Guid(8f07e33567e0ee542b40769c456c6b53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1dbfa429cbf3419cc9ffaaf5d0146cf1') in 0.0008786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.923924 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets/Scripts
  artifactKey: Guid(31bb803a87bc16a4f8153da2e9086604) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets/Scripts using Guid(31bb803a87bc16a4f8153da2e9086604) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '19c2a259a33f5fd68e3a5fc7ba93c150') in 0.0005574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.932965 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets/Scripts/ObjectSpawner.cs
  artifactKey: Guid(956dd6cf70eaca449a45b6a95b96c8c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.1.1/Starter Assets/Scripts/ObjectSpawner.cs using Guid(956dd6cf70eaca449a45b6a95b96c8c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c85851caca987f4441f2ee3893bb2165') in 0.0011297 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 10.93 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 73 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6608 unused Assets / (11.9 MB). Loaded Objects now: 7250.
Memory consumption went from 151.8 MB to 139.9 MB.
Total: 337.366100 ms (FindLiveObjects: 1.089800 ms CreateObjectMapping: 0.890100 ms MarkObjects: 326.589000 ms  DeleteObjects: 8.795500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2868.314188 seconds.
  path: Assets/Asset
  artifactKey: Guid(a91d4beecd056f142a9b075c0ddd5895) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Asset using Guid(a91d4beecd056f142a9b075c0ddd5895) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92812b0575848c3c9ad94515b7b04fe9') in 0.0142649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.753234 seconds.
  path: Assets/AssetManager
  artifactKey: Guid(ae028095adad93942b5c9adc40ce3dac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetManager using Guid(ae028095adad93942b5c9adc40ce3dac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'df03dd81743dfa2ad0c3fcd22234c547') in 0.0005413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.233405 seconds.
  path: Assets/AssetManager/package.json
  artifactKey: Guid(91f69bd954ef595489f7ad3651e9e406) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetManager/package.json using Guid(91f69bd954ef595489f7ad3651e9e406) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b67391f1ea600dc0a7d8088da8533d1') in 0.0445502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.399063 seconds.
  path: Assets/AssetManager/README.md
  artifactKey: Guid(119fe745b74755c488da81e82f675a2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetManager/README.md using Guid(119fe745b74755c488da81e82f675a2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eea1cd063708d293476c3d7662388fb6') in 0.0009827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 296.093411 seconds.
  path: Packages/com.atteneder.gltfast/package.json
  artifactKey: Guid(f9b381701ee454bcdb792099f6b4e4a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/package.json using Guid(f9b381701ee454bcdb792099f6b4e4a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e81fa843272e4a193d5a3a79ac088e01') in 0.0017494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 10.93 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6319 unused Assets / (10.4 MB). Loaded Objects now: 6971.
Memory consumption went from 147.9 MB to 137.6 MB.
Total: 18.517700 ms (FindLiveObjects: 0.771500 ms CreateObjectMapping: 0.993300 ms MarkObjects: 9.569900 ms  DeleteObjects: 7.181600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 9.74 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 73 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6597 unused Assets / (11.6 MB). Loaded Objects now: 7240.
Memory consumption went from 151.8 MB to 140.1 MB.
Total: 17.679400 ms (FindLiveObjects: 0.706700 ms CreateObjectMapping: 1.232700 ms MarkObjects: 7.920200 ms  DeleteObjects: 7.817700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 279.546147 seconds.
  path: Packages/com.atteneder.gltfast/package.json
  artifactKey: Guid(f9b381701ee454bcdb792099f6b4e4a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/package.json using Guid(f9b381701ee454bcdb792099f6b4e4a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15562a7fd57bc146c88b51a645035c7f') in 0.009198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.674987 seconds.
  path: Packages/com.atteneder.gltfast/DocExamples/glTFast.Documentation.Examples.asmdef
  artifactKey: Guid(8f5c1be42eb774f4e8372825ac2e4d50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/DocExamples/glTFast.Documentation.Examples.asmdef using Guid(8f5c1be42eb774f4e8372825ac2e4d50) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1118092167edfc72f612f7411669c2a9') in 0.0005658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.177917 seconds.
  path: Packages/com.atteneder.gltfast/DocExamples/CustomGltfImportPlayables.cs
  artifactKey: Guid(a003718dbc55465498e18c6680de84dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/DocExamples/CustomGltfImportPlayables.cs using Guid(a003718dbc55465498e18c6680de84dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6eaf78e585c6fa1ea922202025ce6887') in 0.0005765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.561911 seconds.
  path: Packages/com.atteneder.gltfast/DocExamples/CustomGameObjectInstantiator.cs
  artifactKey: Guid(8b6f3dca4b87b894f8e2c43bb0ab1b74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/DocExamples/CustomGameObjectInstantiator.cs using Guid(8b6f3dca4b87b894f8e2c43bb0ab1b74) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3ae45754ca90991f4ee80b8edf18861c') in 0.0004857 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.620249 seconds.
  path: Packages/com.atteneder.gltfast/DocExamples/CustomGltfImport.cs
  artifactKey: Guid(ed7eb2828185cf54896d0bc4e34c7d24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/DocExamples/CustomGltfImport.cs using Guid(ed7eb2828185cf54896d0bc4e34c7d24) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b67572648eb3446f4b690aa7efea646d') in 0.0006808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.365230 seconds.
  path: Packages/com.atteneder.gltfast/DocExamples/ExportSamples.cs
  artifactKey: Guid(3bc95897a165b435a9df9b8dabd3f67b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/DocExamples/ExportSamples.cs using Guid(3bc95897a165b435a9df9b8dabd3f67b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32939db0342433daed88a7bf6da8eb0f') in 0.0005519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.892608 seconds.
  path: Packages/com.atteneder.gltfast/Plugins/UnityCloudGltfast.xcprivacy
  artifactKey: Guid(be44ec34bf0bd465eafeb36d836c9015) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/Plugins/UnityCloudGltfast.xcprivacy using Guid(be44ec34bf0bd465eafeb36d836c9015) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da9019d92474703d52177e4e10f98058') in 0.0005019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.034765 seconds.
  path: Packages/com.atteneder.gltfast/Runtime/Scripts/glTFast.asmdef
  artifactKey: Guid(a42927d1d4a3b4cda9b076a7adecb9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/Runtime/Scripts/glTFast.asmdef using Guid(a42927d1d4a3b4cda9b076a7adecb9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '695a201be4258a75817c8da02b32fc4e') in 0.0006055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.961002 seconds.
  path: Packages/com.atteneder.gltfast/Runtime/Scripts/AssemblyInfo.cs
  artifactKey: Guid(5d4869bae2ff044a2a34703990de1679) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/Runtime/Scripts/AssemblyInfo.cs using Guid(5d4869bae2ff044a2a34703990de1679) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '543474b1cd617d3eb6f2d9531b6ca309') in 0.0008431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.266517 seconds.
  path: Packages/com.atteneder.gltfast/Runtime/Scripts/Extensions.cs
  artifactKey: Guid(ac793cf1802844281bf470f4e5ccbaf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.atteneder.gltfast/Runtime/Scripts/Extensions.cs using Guid(ac793cf1802844281bf470f4e5ccbaf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e6d5b0452d6c93bc977ff9f1107a588c') in 0.0005176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 15.51 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 73 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6597 unused Assets / (12.1 MB). Loaded Objects now: 7240.
Memory consumption went from 151.8 MB to 139.7 MB.
Total: 19.687800 ms (FindLiveObjects: 0.957600 ms CreateObjectMapping: 0.841800 ms MarkObjects: 8.764400 ms  DeleteObjects: 9.122200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 16.77 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 73 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6597 unused Assets / (11.8 MB). Loaded Objects now: 7240.
Memory consumption went from 151.8 MB to 139.9 MB.
Total: 18.489600 ms (FindLiveObjects: 0.944700 ms CreateObjectMapping: 0.880100 ms MarkObjects: 8.167200 ms  DeleteObjects: 8.496300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoader.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.xr.openxr/Runtime/OpenXRLoaderInternal.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 78.36 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 73 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6597 unused Assets / (12.8 MB). Loaded Objects now: 7240.
Memory consumption went from 151.8 MB to 138.9 MB.
Total: 47.476200 ms (FindLiveObjects: 1.089200 ms CreateObjectMapping: 1.783700 ms MarkObjects: 19.929400 ms  DeleteObjects: 24.672600 ms)

Prepare: number of updated asset objects reloaded= 0
