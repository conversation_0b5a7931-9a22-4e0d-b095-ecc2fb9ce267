%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2687717463706941668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8635580913397832359}
  - component: {fileID: 2644373859440866025}
  - component: {fileID: 803904461337875641}
  m_Layer: 0
  m_Name: Hands Permissions Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8635580913397832359
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2687717463706941668}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2644373859440866025
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2687717463706941668}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a210aa19b985bf849ba74812660e6f87, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ProcessPermissionsOnStart: 1
  m_PermissionRequests:
  - m_PermissionId: android.permission.HAND_TRACKING
    m_Enabled: 1
    m_Requested: 0
    m_ResponseReceived: 0
    m_Granted: 0
    m_PermissionGranted:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 803904461337875641}
          m_TargetAssemblyTypeName: Unity.VRTemplate.HandSubsystemManager, Assembly-CSharp
          m_MethodName: EnableHandTracking
          m_Mode: 1
          m_Arguments:
            m_ObjectArgument: {fileID: 0}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    m_PermissionDenied:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 803904461337875641}
          m_TargetAssemblyTypeName: Unity.VRTemplate.HandSubsystemManager, Assembly-CSharp
          m_MethodName: DisableHandTracking
          m_Mode: 1
          m_Arguments:
            m_ObjectArgument: {fileID: 0}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
--- !u!114 &803904461337875641
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2687717463706941668}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7d52987a218afbc4c81f20e47e2cb295, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
