{"BuildTarget": 19, "BuildResultHash": "", "BuildType": 0, "BuildStartTime": "21/08/2025 22:01:02", "Duration": 0.0, "BuildError": "SBP ErrorException", "UnityVersion": "6000.0.47f1", "PackageVersion": "com.unity.addressables: 2.3.1", "PlayerBuildVersion": "0.1", "AddressablesEditorSettings": {"SettingsHash": "94630ba72379726e62aafb78ae409837", "ActiveProfile": {"Name": "<PERSON><PERSON><PERSON>", "Id": "0a98f1ec5dbd108468bed1743a78262e", "Values": [{"Key": "0d70c9c1916b24d48afdef49789894e3", "Value": "ServerData/StandaloneWindows64"}, {"Key": "2aa8e19b446980741a5cab8439c7c7e3", "Value": "ServerData/[BuildTarget]"}, {"Key": "510c37c788d15cf4dac1baf9066b83e8", "Value": "{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]"}, {"Key": "6e802af10b135d84e8bf1a71f0e15cfc", "Value": "[UnityEditor.EditorUserBuildSettings.activeBuildTarget]"}, {"Key": "86cc8921cef9e2d4e886a79ad0b9996e", "Value": "[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]"}, {"Key": "e4bcae2dde786a84d8ac1163bde00565", "Value": "http://localhost:5000/assets/[BuildTarget]"}, {"Key": "eda2dff0d7803624297660448e7e5832", "Value": "http://localhost:5000/assets/StandaloneWindows64"}]}, "BuildRemoteCatalog": true, "RemoteCatalogLoadPath": "{UnityEngine.AddressableAssets.Addressables.RuntimePath}/StandaloneWindows64", "BundleLocalCatalog": false, "OptimizeCatalogSize": false, "CatalogRequestsTimeout": 0, "MaxConcurrentWebRequests": 3, "DisableCatalogUpdateOnStartup": false, "UniqueBundleIds": false, "EnableJsonCatalog": false, "NonRecursiveBuilding": true, "ContiguousBundles": true, "DisableSubAssetRepresentations": false, "ShaderBundleNaming": "ProjectName", "MonoScriptBundleNaming": "ProjectName", "StripUnityVersionFromBundleBuild": false}, "AddressablesRuntimeSettings": {"LogResourceManagerExceptions": false, "CatalogLoadPaths": [], "CatalogHash": ""}, "BuildScript": "Default Build Script", "DefaultGroup": {"rid": -2}, "Groups": [], "BuiltInBundles": [], "DuplicatedAssets": [], "LocalCatalogBuildPath": "", "RemoteCatalogBuildPath": "", "references": {"version": 2, "RefIds": [{"rid": -2, "type": {"class": "", "ns": "", "asm": ""}, "data": {}}]}}