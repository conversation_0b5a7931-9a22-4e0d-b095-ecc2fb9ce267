using System.IO;
using UnityEngine;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using UnityEditor.AddressableAssets.Build;
using UnityEditor.AddressableAssets.Build.DataBuilders;
using UnityEditor.AddressableAssets.Build.BuildPipelineTasks;
using Nyxion.AssetManager.Data;
using Nyxion.AssetManager.Settings;

namespace Nyxion.AssetManager.Editor
{
    /// <summary>
    /// Editor tools for building and uploading Addressables content
    /// </summary>
    public class BuildAndUploadAddressables
    {
        [MenuItem("Nyxion/Asset Manager/Build Content")]
        public static void BuildContent()
        {
            BuildAddressablesContent(false);
        }

        [MenuItem("Nyxion/Asset Manager/Update Content")]
        public static void UpdateContent()
        {
            BuildAddressablesContent(true);
        }

        [MenuItem("Nyxion/Asset Manager/Export Catalog Mirror")]
        public static void ExportCatalogMirror()
        {
            ExportCatalogMirrorJson();
        }

        [MenuItem("Nyxion/Asset Manager/Clean Build")]
        public static void CleanBuild()
        {
            CleanAddressablesBuild();
        }

        private static void BuildAddressablesContent(bool isUpdate)
        {
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
            {
                Debug.LogError("Addressables settings not found. Please initialize Addressables first.");
                return;
            }

            try
            {
                Debug.Log($"Starting {(isUpdate ? "update" : "full")} build of Addressables content...");

                AddressablesPlayerBuildResult buildResult;

                if (isUpdate)
                {
                    // For content updates, we'll use a simplified approach
                    Debug.Log("Building content update...");
                    Debug.LogWarning("Content update workflow simplified - performing full build. For advanced content updates, use the Addressables Groups window.");
                    buildResult = AddressableAssetSettings.BuildPlayerContent();
                }
                else
                {
                    // Full build
                    Debug.Log("Building full content...");
                    buildResult = AddressableAssetSettings.BuildPlayerContent();
                }

                if (string.IsNullOrEmpty(buildResult.Error))
                {
                    Debug.Log($"Addressables {(isUpdate ? "update" : "build")} completed successfully!");

                    // Export catalog mirror after successful build
                    ExportCatalogMirrorJson();

                    // Show option to upload
                    if (EditorUtility.DisplayDialog("Build Complete",
                        $"Addressables {(isUpdate ? "update" : "build")} completed successfully!\n\nWould you like to upload to CDN?",
                        "Upload", "Skip"))
                    {
                        UploadToCDN();
                    }
                }
                else
                {
                    Debug.LogError($"Addressables build failed: {buildResult.Error}");
                    EditorUtility.DisplayDialog("Build Failed", $"Addressables build failed:\n{buildResult.Error}", "OK");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Exception during Addressables build: {ex.Message}");
                EditorUtility.DisplayDialog("Build Error", $"An error occurred during build:\n{ex.Message}", "OK");
            }
        }

        private static void ExportCatalogMirrorJson()
        {
            try
            {
                Debug.Log("Exporting catalog mirror JSON...");

                var catalogData = CreateCatalogMirror();
                var json = JsonUtility.ToJson(catalogData, true);

                var exportPath = Path.Combine(Application.dataPath, "../ServerData/catalog_mirror.json");
                var directory = Path.GetDirectoryName(exportPath);
                
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(exportPath, json);
                
                Debug.Log($"Catalog mirror exported to: {exportPath}");
                EditorUtility.RevealInFinder(exportPath);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to export catalog mirror: {ex.Message}");
            }
        }

        private static CatalogData CreateCatalogMirror()
        {
            var catalogData = new CatalogData();
            catalogData.version = System.DateTime.Now.ToString("yyyy.MM.dd.HHmm");

            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
                return catalogData;

            // Scan for materials
            var materialsGroup = settings.FindGroup("Materials");
            if (materialsGroup != null)
            {
                foreach (var entry in materialsGroup.entries)
                {
                    if (entry.MainAsset is Material material)
                    {
                        var materialInfo = new MaterialInfo();
                        materialInfo.key = entry.address.Replace("mat:", "");
                        materialInfo.displayName = material.name;
                        materialInfo.shader = material.shader.name;
                        
                        // Try to find thumbnail
                        var thumbnailPath = $"Thumbnails/{material.name}_thumb";
                        var thumbnailAsset = AssetDatabase.LoadAssetAtPath<Texture2D>($"Assets/Thumbnails/{material.name}_thumb.png");
                        if (thumbnailAsset != null)
                        {
                            materialInfo.thumbnailUrl = $"https://your-cdn.com/thumbnails/{material.name}_thumb.png";
                        }

                        // Extract texture URLs (simplified - you might want to enhance this)
                        if (material.HasProperty("_BaseMap") || material.HasProperty("_MainTex"))
                        {
                            var mainTex = material.GetTexture("_BaseMap") ?? material.GetTexture("_MainTex");
                            if (mainTex != null)
                            {
                                materialInfo.textureUrls["albedo"] = $"https://your-cdn.com/textures/{mainTex.name}.png";
                            }
                        }

                        catalogData.materials.Add(materialInfo);
                    }
                }
            }

            // Scan for prefabs
            var prefabsGroup = settings.FindGroup("Prefabs");
            if (prefabsGroup != null)
            {
                foreach (var entry in prefabsGroup.entries)
                {
                    if (entry.MainAsset is GameObject prefab)
                    {
                        var prefabInfo = new PrefabInfo();
                        prefabInfo.key = entry.address;
                        prefabInfo.displayName = prefab.name;
                        prefabInfo.category = "Default"; // You might want to extract this from folder structure
                        prefabInfo.assetAddressOrUrl = entry.address;
                        
                        // Determine pipeline based on settings
                        var remoteSettings = Resources.Load<RemoteContentSettings>("RemoteContentSettings");
                        prefabInfo.pipeline = remoteSettings?.Pipeline.ToString() ?? "URP";

                        // Try to find preview image
                        var previewPath = $"Previews/{prefab.name}_preview.png";
                        var previewAsset = AssetDatabase.LoadAssetAtPath<Texture2D>($"Assets/Previews/{prefab.name}_preview.png");
                        if (previewAsset != null)
                        {
                            prefabInfo.previewUrl = $"https://your-cdn.com/previews/{prefab.name}_preview.png";
                        }

                        // Extract material slots from renderers
                        var renderers = prefab.GetComponentsInChildren<Renderer>();
                        foreach (var renderer in renderers)
                        {
                            for (int i = 0; i < renderer.sharedMaterials.Length; i++)
                            {
                                var material = renderer.sharedMaterials[i];
                                if (material != null)
                                {
                                    var slot = new MaterialSlot();
                                    slot.slotName = renderer.name;
                                    slot.materialKey = material.name; // You might want to use a more sophisticated mapping
                                    prefabInfo.materialSlots.Add(slot);
                                }
                            }
                        }

                        catalogData.prefabs.Add(prefabInfo);
                    }
                }
            }

            return catalogData;
        }

        private static void UploadToCDN()
        {
            var remoteSettings = Resources.Load<RemoteContentSettings>("RemoteContentSettings");
            if (remoteSettings == null)
            {
                Debug.LogWarning("RemoteContentSettings not found. Cannot upload to CDN.");
                return;
            }

            // This is a placeholder for CDN upload functionality
            // In a real implementation, you would:
            // 1. Get the build output directory
            // 2. Upload files to your CDN (AWS S3, Azure Blob, etc.)
            // 3. Update the catalog on your Flask server

            var buildPath = Path.Combine(Application.dataPath, "../ServerData");
            
            Debug.Log($"CDN Upload placeholder - Build path: {buildPath}");
            Debug.Log($"Remote Load Path: {remoteSettings.GetRemoteLoadPath()}");
            
            EditorUtility.DisplayDialog("Upload Placeholder", 
                "CDN upload functionality is not implemented in this example.\n\n" +
                "You would typically upload the contents of ServerData folder to your CDN here.", 
                "OK");
        }

        private static void CleanAddressablesBuild()
        {
            try
            {
                Debug.Log("Cleaning Addressables build...");

                var settings = AddressableAssetSettingsDefaultObject.Settings;
                if (settings != null)
                {
                    AddressableAssetSettings.CleanPlayerContent(settings.ActivePlayerDataBuilder);
                }

                // Clean ServerData folder
                var serverDataPath = Path.Combine(Application.dataPath, "../ServerData");
                if (Directory.Exists(serverDataPath))
                {
                    Directory.Delete(serverDataPath, true);
                    Debug.Log("Cleaned ServerData folder");
                }

                Debug.Log("Clean completed successfully!");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to clean build: {ex.Message}");
            }
        }
    }
}
