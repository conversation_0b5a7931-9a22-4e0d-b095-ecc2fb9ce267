-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/glTFast.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/glTFast.ref.dll"
-define:UNITY_6000_0_47
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:USE_INPUT_SYSTEM_POSE_CONTROL
-define:UNITY_POST_PROCESSING_STACK_V2
-define:USE_STICK_CONTROL_THUMBSTICKS
-define:USING_URP
-define:UNITY_PHYSICS
-define:UNITY_ANIMATION
-define:UNITY_IMAGECONVERSION
-define:UNITY_WEBREQUEST_TEXTURE
-define:UNITY_COLLECTIONS
-define:USING_URP_12_OR_NEWER
-define:UNITY_SHADER_GRAPH
-define:UNITY_SHADER_GRAPH_12_OR_NEWER
-define:UNITY_MATH_1_3_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@8a82222c5449/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@8a82222c5449/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@8a82222c5449/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@8a82222c5449/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/glTFast.Animation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/AccessorUsage.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/AnimationMethod.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/AnimationUtils.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/AssemblyInfo.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ComponentType.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/DefaultDeferAgent.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/DontDestroyOnLoad.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/DracoMeshGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Extensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/FlatArray.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GameObjectBoundsInstantiator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GameObjectInstantiator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GameObjectSceneInstance.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GlbBinChunk.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GltfAsset.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GltfAssetBase.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GltfBoundsAsset.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GltfGlobals.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GltfImport.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/GltfJsonUtilityParser.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/IDeferAgent.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/IGltfBuffers.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/IGltfReadable.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/IInstantiator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ImageCreateContext.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/IMaterialProvider.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/IMaterialsVariantsProvider.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ImportAddons/ImportAddon.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ImportAddons/ImportAddonInstance.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ImportAddons/ImportAddonRegistry.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ImportSettings.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/InstantiationSettings.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Jobs.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/KtxLoadContext.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/KtxLoadContextBase.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/KtxLoadNativeContext.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/LightPunctualExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Loading/CustomHeaderDownloadProvider.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Loading/DefaultDownloadProvider.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Loading/IDownload.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Loading/IDownloadProvider.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Loading/INativeDownload.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Loading/ITextureDownload.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Logging/CollectingLogger.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Logging/ConsoleLogger.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Logging/ICodeLogger.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Logging/LogMessages.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MainBufferType.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/ManagedNativeArray.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/BuiltInMaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/BuiltInShaderGraphMaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/Constants.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/HighDefinitionRPMaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/IMaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/MaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/MaterialProperty.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/ShaderGraphMaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/StandardShaderMode.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Material/UniveralRPMaterialGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MaterialsVariants/IMaterialsVariantsSlot.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MaterialsVariants/IMaterialsVariantsSlotInstance.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MaterialsVariants/MaterialsVariantsComponent.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MaterialsVariants/MaterialsVariantsControl.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MaterialsVariants/MaterialsVariantsSlotInstances.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MaterialsVariants/MultiMaterialsVariantsSlotInstances.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Mathematics.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshAssignment.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshComparer.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshGeneratorBase.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshOrder.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshResult.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MeshSubset.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/MorphTargetsGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/NameImportMethod.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/NativeArrayExtensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/NodeExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/PrimitiveComparer.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/PrimitivesComparer.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/PrimitiveSet.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/RenderPipelineUtils.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/RootExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/SamplerKey.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/SceneObjectCreation.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Accessor.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/AccessorSparse.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/AccessorSparseIndices.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/AccessorSparseValues.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/AnimationChannel.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/AnimationChannelTarget.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/AnimationSampler.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Asset.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Buffer.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/BufferView.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/BufferViewExtensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/BufferViewMeshoptExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Constants.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/Accessor.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/AccessorSparse.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/Material.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/MaterialExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/Mesh.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/MeshPrimitive.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/NamedObject.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/FakeSchema/Root.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/GltfAccessorAttributeType.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/GltfAnimation.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/GltfCamera.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/GltfComponentType.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/IBufferView.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Image.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/InterpolationType.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/JsonWriter.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/LightPunctual.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/LightsPunctual.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Material.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialClearCoat.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialExtensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialIor.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialPbrMetallicRoughness.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialSheen.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialSpecular.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialsVariantsExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialTransmission.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MaterialUnlit.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Mesh.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MeshGpuInstancing.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/MeshPrimitive.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/NamedObject.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Node.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/NodeLightsPunctual.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/NormalTextureInfo.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/OcclusionTextureInfo.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/PbrSpecularGlossiness.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Root.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/RootExtensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Sampler.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Scene.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Skin.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/SpotLight.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/Texture.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/TextureExtensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/TextureInfo.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/TextureInfoExtensions.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/Schema/TextureTransform.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/StreamExtension.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/SubMeshAssignment.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/TextureDownload.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/TimeBudgetPerFrameDeferAgent.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/UninterruptedDefaultDeferAgent.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/UninterruptedDeferAgent.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/UriHelper.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/UvTransform.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexBufferBones.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexBufferColors.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexBufferDescriptor.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexBufferGenerator.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexBufferGeneratorBase.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexBufferTexCoords.cs"
"Library/PackageCache/com.atteneder.gltfast@b5db919fe5d8/Runtime/Scripts/VertexStructs.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/glTFast.UnityAdditionalFile.txt"