{"name": "glTFast.Documentation.Examples", "rootNamespace": "GLTFast.Documentation.Examples", "references": ["Unity.Mathematics", "glTFast", "glTFast.Export", "glTFast.Newtonsoft"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [{"name": "com.unity.nuget.newtonsoft-json", "expression": "3", "define": "NEWTONSOFT_JSON"}, {"name": "com.unity.modules.animation", "expression": "1.0.0", "define": "UNITY_ANIMATION"}], "noEngineReferences": false}