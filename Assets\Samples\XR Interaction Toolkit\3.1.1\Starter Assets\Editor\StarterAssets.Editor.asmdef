{"name": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor", "rootNamespace": "", "references": ["Unity.XR.Interaction.Toolkit", "Unity.XR.Interaction.Toolkit.Editor", "Unity.XR.CoreUtils", "Unity.XR.CoreUtils.Editor", "Unity.InputSystem"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "Unity", "expression": "2022.3", "define": "UNITY_INPUT_SYSTEM_PROJECT_WIDE_ACTIONS"}], "noEngineReferences": false}