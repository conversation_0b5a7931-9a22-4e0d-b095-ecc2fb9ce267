{"name": "glTFast", "rootNamespace": "GLTFast", "references": ["Unity.Mathematics", "Unity.Burst", "Unity.Collections", "Unity.Jobs", "Draco", "Ktx", "Unity.Meshopt.Decompress", "Unity.RenderPipelines.Universal.Runtime", "Unity.RenderPipelines.HighDefinition.Runtime", "glTFast.Animation"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.cloud.draco", "expression": "5.0.0-pre.1", "define": "DRACO_UNITY"}, {"name": "com.unity.cloud.ktx", "expression": "", "define": "KTX_UNITY"}, {"name": "com.unity.cloud.ktx", "expression": "1.3.0", "define": "KTX_UNITY_1_3_OR_NEWER"}, {"name": "com.unity.cloud.ktx", "expression": "2.2.1", "define": "KTX_UNITY_2_2_OR_NEWER"}, {"name": "com.unity.render-pipelines.universal", "expression": "7.3.1", "define": "USING_URP"}, {"name": "com.unity.render-pipelines.high-definition", "expression": "7.3.1", "define": "USING_HDRP"}, {"name": "com.unity.render-pipelines.high-definition", "expression": "10.0.0", "define": "USING_HDRP_10_OR_NEWER"}, {"name": "com.unity.render-pipelines.high-definition", "expression": "17", "define": "USING_HDRP_17_OR_NEWER"}, {"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "UNITY_PHYSICS"}, {"name": "com.unity.modules.animation", "expression": "1.0.0", "define": "UNITY_ANIMATION"}, {"name": "com.unity.modules.imageconversion", "expression": "", "define": "UNITY_IMAGECONVERSION"}, {"name": "com.unity.modules.unitywebrequesttexture", "expression": "", "define": "UNITY_WEBREQUEST_TEXTURE"}, {"name": "com.unity.collections", "expression": "1.5.0", "define": "UNITY_COLLECTIONS"}, {"name": "com.unity.meshopt.decompress", "expression": "", "define": "MESHOPT"}, {"name": "com.unity.render-pipelines.universal", "expression": "12", "define": "USING_URP_12_OR_NEWER"}, {"name": "com.unity.shadergraph", "expression": "", "define": "UNITY_SHADER_GRAPH"}, {"name": "com.unity.shadergraph", "expression": "12", "define": "UNITY_SHADER_GRAPH_12_OR_NEWER"}, {"name": "com.unity.nuget.newtonsoft-json", "expression": "3", "define": "NEWTONSOFT_JSON"}, {"name": "com.unity.mathematics", "expression": "1.3", "define": "UNITY_MATH_1_3_OR_NEWER"}], "noEngineReferences": false}