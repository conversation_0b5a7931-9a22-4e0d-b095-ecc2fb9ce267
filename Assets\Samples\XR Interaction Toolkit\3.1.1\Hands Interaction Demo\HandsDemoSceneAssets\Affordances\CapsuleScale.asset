%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6f7c05e2d6a94808ac5dcf4531f19721, type: 3}
  m_Name: CapsuleScale
  m_EditorClassIdentifier: 
  m_Comments: 
  m_ReadOnly: 1
  m_Value:
    m_StateAnimationCurve:
      m_UseConstant: 1
      m_ConstantValue:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_Variable: {fileID: 0}
    m_List:
    - stateName: disabled
      animationStateStartValue:
        x: 0
        y: 0
        z: 0
      animationStateEndValue:
        x: 0
        y: 0
        z: 0
    - stateName: idle
      animationStateStartValue:
        x: 0.5
        y: 0.5
        z: 0.5
      animationStateEndValue:
        x: 0.5
        y: 0.5
        z: 0.5
    - stateName: hovered
      animationStateStartValue:
        x: 0.5
        y: 2
        z: 0.5
      animationStateEndValue:
        x: 0.5
        y: 2
        z: 0.5
    - stateName: hoveredPriority
      animationStateStartValue:
        x: 0.5
        y: 2
        z: 0.5
      animationStateEndValue:
        x: 0.5
        y: 2
        z: 0.5
    - stateName: selected
      animationStateStartValue:
        x: 0.5
        y: 2
        z: 0.5
      animationStateEndValue:
        x: 0.5
        y: 2
        z: 0.5
    - stateName: activated
      animationStateStartValue:
        x: 0.5
        y: 2
        z: 0.5
      animationStateEndValue:
        x: 0.5
        y: 2
        z: 0.5
    - stateName: focused
      animationStateStartValue:
        x: 0.5
        y: 0.5
        z: 0.5
      animationStateEndValue:
        x: 0.5
        y: 0.5
        z: 0.5
