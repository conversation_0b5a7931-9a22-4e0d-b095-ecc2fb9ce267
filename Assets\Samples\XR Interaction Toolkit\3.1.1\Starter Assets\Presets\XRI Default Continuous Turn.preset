%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Continuous Turn
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: 75b29b6c6428c984a8a73ffc2d58063b, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Mediator
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TransformationPriority
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TurnSpeed
    value: 60
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Name
    value: Left Hand Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Id
    value: 3610965d-108d-4451-a143-a78d1ee8f9b8
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputActionReference
    value: 
    objectReference: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_ManualValue.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_ManualValue.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Name
    value: Right Hand Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Id
    value: eeb82678-2af4-4b6c-87fc-621bb707edc5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputActionReference
    value: 
    objectReference: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_ManualValue.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_ManualValue.y
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
