using UnityEngine;
using UnityEditor;
using UnityEngine.AddressableAssets;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using UnityEditor.AddressableAssets.Settings.GroupSchemas;
using Nyxion.AssetManager.Settings;

namespace Nyxion.AssetManager.Editor
{
    /// <summary>
    /// Editor window for one-click Addressables setup
    /// </summary>
    public class AddressablesSetupWindow : EditorWindow
    {
        private RemoteContentSettings _settings;
        private Vector2 _scrollPosition;
        private bool _showAdvancedSettings = false;

        [MenuItem("Nyxion/Asset Manager/Setup Addressables")]
        public static void ShowWindow()
        {
            var window = GetWindow<AddressablesSetupWindow>("Addressables Setup");
            window.minSize = new Vector2(400, 500);
            window.Show();
        }

        private void OnEnable()
        {
            LoadSettings();
        }

        private void LoadSettings()
        {
            // Try to load from Resources folder first
            _settings = Resources.Load<RemoteContentSettings>("RemoteContentSettings");

            // If not found, try to load from the direct path
            if (_settings == null)
            {
                _settings = AssetDatabase.LoadAssetAtPath<RemoteContentSettings>("Assets/AssetManager/Runtime/Resources/RemoteContentSettings.asset");
            }

            // If still not found, we'll show the create button in the UI
            if (_settings == null)
            {
                Debug.LogWarning("RemoteContentSettings not found in Resources folder. Use the 'Create Settings Asset' button to create one.");
            }
            else
            {
                Debug.Log("RemoteContentSettings loaded successfully");
            }
        }

        private void OnGUI()
        {
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

            EditorGUILayout.LabelField("Nyxion Asset Manager Setup", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            DrawSettingsSection();
            EditorGUILayout.Space();
            DrawSetupSection();
            EditorGUILayout.Space();
            DrawStatusSection();

            EditorGUILayout.EndScrollView();
        }

        private void DrawSettingsSection()
        {
            EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
            
            if (_settings == null)
            {
                EditorGUILayout.HelpBox("RemoteContentSettings not found. Please create one first.", MessageType.Warning);
                if (GUILayout.Button("Create Settings Asset"))
                {
                    CreateSettingsAsset();
                }
                return;
            }

            EditorGUI.BeginChangeCheck();

            _settings.CatalogUri = EditorGUILayout.TextField("Catalog URI", _settings.CatalogUri);
            _settings.AddressablesRemoteLoadPath = EditorGUILayout.TextField("Remote Load Path", _settings.AddressablesRemoteLoadPath);
            _settings.AddressablesRemoteBuildPath = EditorGUILayout.TextField("Remote Build Path", _settings.AddressablesRemoteBuildPath);
            _settings.Pipeline = (RenderPipeline)EditorGUILayout.EnumPopup("Pipeline", _settings.Pipeline);

            _showAdvancedSettings = EditorGUILayout.Foldout(_showAdvancedSettings, "Advanced Settings");
            if (_showAdvancedSettings)
            {
                EditorGUI.indentLevel++;
                _settings.AutoUpdateCatalogOnStart = EditorGUILayout.Toggle("Auto Update Catalog", _settings.AutoUpdateCatalogOnStart);
                _settings.MaxConcurrentDownloads = EditorGUILayout.IntSlider("Max Concurrent Downloads", _settings.MaxConcurrentDownloads, 1, 10);
                _settings.DownloadTimeoutSeconds = EditorGUILayout.IntSlider("Download Timeout (s)", _settings.DownloadTimeoutSeconds, 10, 300);
                _settings.EnableOfflineMode = EditorGUILayout.Toggle("Enable Offline Mode", _settings.EnableOfflineMode);
                _settings.MaxCacheSizeMB = EditorGUILayout.IntSlider("Max Cache Size (MB)", _settings.MaxCacheSizeMB, 0, 10000);
                _settings.AutoClearCache = EditorGUILayout.Toggle("Auto Clear Cache", _settings.AutoClearCache);
                EditorGUI.indentLevel--;
            }

            if (EditorGUI.EndChangeCheck())
            {
                EditorUtility.SetDirty(_settings);
            }

            if (GUILayout.Button("Save Settings"))
            {
                AssetDatabase.SaveAssets();
                EditorGUILayout.HelpBox("Settings saved successfully!", MessageType.Info);
            }
        }

        private void DrawSetupSection()
        {
            EditorGUILayout.LabelField("Setup Actions", EditorStyles.boldLabel);

            if (GUILayout.Button("Initialize Addressables", GUILayout.Height(30)))
            {
                InitializeAddressables();
            }

            if (GUILayout.Button("Create Material Groups", GUILayout.Height(30)))
            {
                CreateMaterialGroups();
            }

            if (GUILayout.Button("Create Prefab Groups", GUILayout.Height(30)))
            {
                CreatePrefabGroups();
            }

            if (GUILayout.Button("Configure Remote Paths", GUILayout.Height(30)))
            {
                ConfigureRemotePaths();
            }

            EditorGUILayout.Space();

            if (GUILayout.Button("Complete Setup (All Steps)", GUILayout.Height(40)))
            {
                CompleteSetup();
            }
        }

        private void DrawStatusSection()
        {
            EditorGUILayout.LabelField("Status", EditorStyles.boldLabel);

            var addressableSettings = AddressableAssetSettingsDefaultObject.Settings;
            if (addressableSettings == null)
            {
                EditorGUILayout.HelpBox("Addressables not initialized", MessageType.Warning);
                return;
            }

            // Display basic info (removed playerBuildVersion as it doesn't exist in newer versions)
            EditorGUILayout.LabelField($"Groups Count: {addressableSettings.groups.Count}");
            EditorGUILayout.LabelField($"Active Profile: {addressableSettings.profileSettings.GetProfileName(addressableSettings.activeProfileId)}");

            var materialsGroup = addressableSettings.FindGroup("Materials");
            var prefabsGroup = addressableSettings.FindGroup("Prefabs");
            var thumbnailsGroup = addressableSettings.FindGroup("Thumbnails");

            EditorGUILayout.LabelField($"Materials Group: {(materialsGroup != null ? "✓" : "✗")}");
            EditorGUILayout.LabelField($"Prefabs Group: {(prefabsGroup != null ? "✓" : "✗")}");
            EditorGUILayout.LabelField($"Thumbnails Group: {(thumbnailsGroup != null ? "✓" : "✗")}");

            // Display profile settings
            try
            {
                var remoteLoadPath = addressableSettings.profileSettings.GetValueByName(addressableSettings.activeProfileId, "RemoteLoadPath");
                var remoteBuildPath = addressableSettings.profileSettings.GetValueByName(addressableSettings.activeProfileId, "RemoteBuildPath");

                EditorGUILayout.LabelField($"Remote Load Path: {remoteLoadPath ?? "Not set"}");
                EditorGUILayout.LabelField($"Remote Build Path: {remoteBuildPath ?? "Not set"}");
            }
            catch (System.Exception ex)
            {
                EditorGUILayout.LabelField($"Profile Error: {ex.Message}");
            }
        }

        private void CreateSettingsAsset()
        {
            // Create the settings instance
            var settings = CreateInstance<RemoteContentSettings>();

            // Set default values
            settings.CatalogUri = "http://localhost:5000/catalog";
            settings.AddressablesRemoteLoadPath = "http://localhost:5000/assets/[BuildTarget]";
            settings.AddressablesRemoteBuildPath = "ServerData/[BuildTarget]";
            settings.Pipeline = RenderPipeline.URP;
            settings.AutoUpdateCatalogOnStart = true;

            // Ensure Resources folder exists
            if (!AssetDatabase.IsValidFolder("Assets/AssetManager/Runtime/Resources"))
            {
                AssetDatabase.CreateFolder("Assets/AssetManager/Runtime", "Resources");
            }

            // Create the asset
            string assetPath = "Assets/AssetManager/Runtime/Resources/RemoteContentSettings.asset";
            AssetDatabase.CreateAsset(settings, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Load the created asset to ensure it's properly referenced
            _settings = AssetDatabase.LoadAssetAtPath<RemoteContentSettings>(assetPath);

            if (_settings != null)
            {
                Debug.Log("Successfully created RemoteContentSettings asset with default values");
            }
            else
            {
                Debug.LogError("Failed to create RemoteContentSettings asset");
            }
        }

        private void InitializeAddressables()
        {
            try
            {
                AddressableAssetSettings.Create(AddressableAssetSettingsDefaultObject.kDefaultConfigFolder, 
                                              AddressableAssetSettingsDefaultObject.kDefaultConfigAssetName, 
                                              true, true);
                Debug.Log("Addressables initialized successfully");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to initialize Addressables: {ex.Message}");
            }
        }

        private void CreateMaterialGroups()
        {
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
            {
                Debug.LogError("Addressables not initialized. Please initialize first.");
                return;
            }

            // Create Materials group
            var materialsGroup = settings.FindGroup("Materials");
            if (materialsGroup == null)
            {
                materialsGroup = settings.CreateGroup("Materials", false, false, true, null, typeof(ContentUpdateGroupSchema), typeof(BundledAssetGroupSchema));
                
                var bundledSchema = materialsGroup.GetSchema<BundledAssetGroupSchema>();
                if (bundledSchema != null)
                {
                    bundledSchema.BuildPath.SetVariableByName(settings, AddressableAssetSettings.kRemoteBuildPath);
                    bundledSchema.LoadPath.SetVariableByName(settings, AddressableAssetSettings.kRemoteLoadPath);
                    bundledSchema.BundleMode = BundledAssetGroupSchema.BundlePackingMode.PackSeparately;
                }

                Debug.Log("Created Materials group");
            }

            // Create Thumbnails group
            var thumbnailsGroup = settings.FindGroup("Thumbnails");
            if (thumbnailsGroup == null)
            {
                thumbnailsGroup = settings.CreateGroup("Thumbnails", false, false, true, null, typeof(ContentUpdateGroupSchema), typeof(BundledAssetGroupSchema));
                
                var bundledSchema = thumbnailsGroup.GetSchema<BundledAssetGroupSchema>();
                if (bundledSchema != null)
                {
                    bundledSchema.BuildPath.SetVariableByName(settings, AddressableAssetSettings.kRemoteBuildPath);
                    bundledSchema.LoadPath.SetVariableByName(settings, AddressableAssetSettings.kRemoteLoadPath);
                }

                Debug.Log("Created Thumbnails group");
            }

            EditorUtility.SetDirty(settings);
        }

        private void CreatePrefabGroups()
        {
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
            {
                Debug.LogError("Addressables not initialized. Please initialize first.");
                return;
            }

            var prefabsGroup = settings.FindGroup("Prefabs");
            if (prefabsGroup == null)
            {
                prefabsGroup = settings.CreateGroup("Prefabs", false, false, true, null, typeof(ContentUpdateGroupSchema), typeof(BundledAssetGroupSchema));
                
                var bundledSchema = prefabsGroup.GetSchema<BundledAssetGroupSchema>();
                if (bundledSchema != null)
                {
                    bundledSchema.BuildPath.SetVariableByName(settings, AddressableAssetSettings.kRemoteBuildPath);
                    bundledSchema.LoadPath.SetVariableByName(settings, AddressableAssetSettings.kRemoteLoadPath);
                }

                Debug.Log("Created Prefabs group");
            }

            EditorUtility.SetDirty(settings);
        }

        private void ConfigureRemotePaths()
        {
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null || _settings == null)
            {
                Debug.LogError("Addressables or settings not available");
                return;
            }

            var profileId = settings.activeProfileId;
            var profileSettings = settings.profileSettings;

            // Create RemoteLoadPath variable if it doesn't exist
            if (profileSettings.GetVariableNames().IndexOf("RemoteLoadPath") < 0)
            {
                profileSettings.CreateValue("RemoteLoadPath", _settings.GetRemoteLoadPath());
                Debug.Log("Created RemoteLoadPath profile variable");
            }
            else
            {
                profileSettings.SetValue(profileId, "RemoteLoadPath", _settings.GetRemoteLoadPath());
            }

            // Create RemoteBuildPath variable if it doesn't exist
            if (profileSettings.GetVariableNames().IndexOf("RemoteBuildPath") < 0)
            {
                profileSettings.CreateValue("RemoteBuildPath", _settings.GetRemoteBuildPath());
                Debug.Log("Created RemoteBuildPath profile variable");
            }
            else
            {
                profileSettings.SetValue(profileId, "RemoteBuildPath", _settings.GetRemoteBuildPath());
            }

            EditorUtility.SetDirty(settings);
            Debug.Log("Remote paths configured successfully");
        }

        private void CompleteSetup()
        {
            InitializeAddressables();
            CreateMaterialGroups();
            CreatePrefabGroups();
            ConfigureRemotePaths();
            
            Debug.Log("Complete Addressables setup finished!");
            EditorUtility.DisplayDialog("Setup Complete", "Addressables has been configured for the Asset Manager system.", "OK");
        }
    }
}
