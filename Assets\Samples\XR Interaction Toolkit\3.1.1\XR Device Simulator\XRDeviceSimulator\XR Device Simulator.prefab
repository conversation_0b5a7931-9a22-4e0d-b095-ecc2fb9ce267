%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6598815579406187037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6598815579406187027}
  - component: {fileID: 6598815579406187026}
  - component: {fileID: 8477477680821523469}
  - component: {fileID: 1689558038395899221}
  m_Layer: 0
  m_Name: XR Device Simulator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6598815579406187027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6598815579406187037}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6598815579406187026
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6598815579406187037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b34befe5d0cbb642bb5d09104a47160, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_DeviceSimulatorActionAsset: {fileID: -944628639613478452, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ControllerActionAsset: {fileID: -944628639613478452, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_KeyboardXTranslateAction: {fileID: -2435995061748527091, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_KeyboardYTranslateAction: {fileID: 4091624078112751379, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_KeyboardZTranslateAction: {fileID: 8957443236229058949, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ManipulateLeftAction: {fileID: 3215650258570939094, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ManipulateRightAction: {fileID: 138396950478516224, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleManipulateLeftAction: {fileID: 2547216639932606815, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleManipulateRightAction: {fileID: 743384497930276301, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleManipulateBodyAction: {fileID: -658012382136555628, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ManipulateHeadAction: {fileID: -3619485213038975404, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_HandControllerModeAction: {fileID: -6730069882215067947, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_CycleDevicesAction: {fileID: -7837977739890211585, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_StopManipulationAction: {fileID: 1698315126802870675, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_MouseDeltaAction: {fileID: -1273072440521047205, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_MouseScrollAction: {fileID: 4546399164687744209, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_RotateModeOverrideAction: {fileID: -8754530952185592012, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleMouseTransformationModeAction: {fileID: 3100586429251580691, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_NegateModeAction: {fileID: 1882878426541990298, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_XConstraintAction: {fileID: -8086843181801629294, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_YConstraintAction: {fileID: 5691479700773754790, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ZConstraintAction: {fileID: 1644704167276153141, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ResetAction: {fileID: -2638007419058092452, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleCursorLockAction: {fileID: -2382836779261746822, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleDevicePositionTargetAction: {fileID: -6716103979869350223, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_TogglePrimary2DAxisTargetAction: {fileID: -7682297331024740639, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_ToggleSecondary2DAxisTargetAction: {fileID: 1155009490345466815, guid: da2b439d1a2de5c46a4f428f8cf4fe19, type: 3}
  m_Axis2DAction: {fileID: 8275859971367427353, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_RestingHandAxis2DAction: {fileID: 6756245720351945193, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_GripAction: {fileID: 5667446173830999989, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_TriggerAction: {fileID: -2439264783773714294, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_PrimaryButtonAction: {fileID: -3599823989380923159, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_SecondaryButtonAction: {fileID: -8069514856583376848, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_MenuAction: {fileID: 4116954447336496447, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_Primary2DAxisClickAction: {fileID: 637922521265743415, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_Secondary2DAxisClickAction: {fileID: -8358032100899166728, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_Primary2DAxisTouchAction: {fileID: 2883175194488637904, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_Secondary2DAxisTouchAction: {fileID: -851591506940895311, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_PrimaryTouchAction: {fileID: -4201894270441249665, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_SecondaryTouchAction: {fileID: 5188782311186578770, guid: 0be0028c24f2a4c14a96b6aa39055933, type: 3}
  m_HandActionAsset: {fileID: -944628639613478452, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
  m_CameraTransform: {fileID: 0}
  m_KeyboardTranslateSpace: 0
  m_MouseTranslateSpace: 2
  m_KeyboardXTranslateSpeed: 0.2
  m_KeyboardYTranslateSpeed: 0.2
  m_KeyboardZTranslateSpeed: 0.2
  m_KeyboardBodyTranslateMultiplier: 5
  m_MouseXTranslateSensitivity: 0.0004
  m_MouseYTranslateSensitivity: 0.0004
  m_MouseScrollTranslateSensitivity: 0.0002
  m_MouseXRotateSensitivity: 0.2
  m_MouseYRotateSensitivity: 0.2
  m_MouseScrollRotateSensitivity: 0.05
  m_MouseYRotateInvert: 0
  m_DesiredCursorLockMode: 1
  m_DeviceSimulatorUI: {fileID: 7662076761675301960, guid: ead42e0472b7547fbba6c229aeaf37d3, type: 3}
  m_GripAmount: 1
  m_TriggerAmount: 1
  m_HMDIsTracked: 1
  m_HMDTrackingState: 3
  m_LeftControllerIsTracked: 1
  m_LeftControllerTrackingState: 3
  m_RightControllerIsTracked: 1
  m_RightControllerTrackingState: 3
  m_LeftHandIsTracked: 1
  m_RightHandIsTracked: 1
  m_RestingHandExpressionCapture: {fileID: 0}
  m_SimulatedHandExpressions: []
--- !u!114 &8477477680821523469
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6598815579406187037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a798ede3f9ef495cb5b62f2a9896b2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RemoveOtherHMDDevices: 1
  m_HandTrackingCapability: 1
--- !u!114 &1689558038395899221
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6598815579406187037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 18d5c629281874bc18cb5ead1d245f6a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SimulatedHandExpressions:
  - m_Name: Poke
    m_ToggleInput:
      m_InputSourceMode: 2
      m_InputActionPerformed:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: aac4fcd2-f835-44f9-ad9e-717f1d0a56a1
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionValue:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: c11ef2db-7277-4707-a130-c362223114a8
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionReferencePerformed: {fileID: -5976510165261225158, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_InputActionReferenceValue: {fileID: -5976510165261225158, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_ObjectReferenceObject: {fileID: 0}
      m_ManualPerformed: 0
      m_ManualValue: 0
      m_ManualQueuePerformed: 0
      m_ManualQueueWasPerformedThisFrame: 0
      m_ManualQueueWasCompletedThisFrame: 0
      m_ManualQueueValue: 0
      m_ManualQueueTargetFrame: 0
    m_Capture: {fileID: 11400000, guid: 95c319715e9d2644da8ae09af8ccfee6, type: 2}
  - m_Name: Pinch
    m_ToggleInput:
      m_InputSourceMode: 2
      m_InputActionPerformed:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 76db0311-b6ef-41ff-bbaf-1dac59f943dc
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionValue:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 86573393-89f5-448d-8c62-8ab352958afd
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionReferencePerformed: {fileID: -8306000708137014372, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_InputActionReferenceValue: {fileID: -8306000708137014372, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_ObjectReferenceObject: {fileID: 0}
      m_ManualPerformed: 0
      m_ManualValue: 0
      m_ManualQueuePerformed: 0
      m_ManualQueueWasPerformedThisFrame: 0
      m_ManualQueueWasCompletedThisFrame: 0
      m_ManualQueueValue: 0
      m_ManualQueueTargetFrame: 0
    m_Capture: {fileID: 11400000, guid: d6e15a52475c2564ca7d2977fdece24a, type: 2}
  - m_Name: Grab
    m_ToggleInput:
      m_InputSourceMode: 2
      m_InputActionPerformed:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: a1f98a06-df3d-4feb-b1da-206a5f661aa8
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionValue:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 1713ae4a-66fb-4348-af45-de3be63a5454
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionReferencePerformed: {fileID: -4373459253818063952, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_InputActionReferenceValue: {fileID: -4373459253818063952, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_ObjectReferenceObject: {fileID: 0}
      m_ManualPerformed: 0
      m_ManualValue: 0
      m_ManualQueuePerformed: 0
      m_ManualQueueWasPerformedThisFrame: 0
      m_ManualQueueWasCompletedThisFrame: 0
      m_ManualQueueValue: 0
      m_ManualQueueTargetFrame: 0
    m_Capture: {fileID: 11400000, guid: 3861c298d39c60c44b16920421444875, type: 2}
  - m_Name: Thumb
    m_ToggleInput:
      m_InputSourceMode: 2
      m_InputActionPerformed:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 5762c19a-008c-42f8-8f5b-3b36804b8137
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionValue:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: abe80d9c-1cdb-4099-acb0-77a27f861033
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionReferencePerformed: {fileID: -741559036651486339, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_InputActionReferenceValue: {fileID: -741559036651486339, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_ObjectReferenceObject: {fileID: 0}
      m_ManualPerformed: 0
      m_ManualValue: 0
      m_ManualQueuePerformed: 0
      m_ManualQueueWasPerformedThisFrame: 0
      m_ManualQueueWasCompletedThisFrame: 0
      m_ManualQueueValue: 0
      m_ManualQueueTargetFrame: 0
    m_Capture: {fileID: 11400000, guid: 9d8c9c84da35a7c4c89efd57343c1df8, type: 2}
  - m_Name: Open
    m_ToggleInput:
      m_InputSourceMode: 2
      m_InputActionPerformed:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 48f4382d-8c5f-4e39-83ef-405d16e851a8
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionValue:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 3a364d19-e512-4a45-a31d-ff022b11485f
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionReferencePerformed: {fileID: -9192331390769138535, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_InputActionReferenceValue: {fileID: -9192331390769138535, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_ObjectReferenceObject: {fileID: 0}
      m_ManualPerformed: 0
      m_ManualValue: 0
      m_ManualQueuePerformed: 0
      m_ManualQueueWasPerformedThisFrame: 0
      m_ManualQueueWasCompletedThisFrame: 0
      m_ManualQueueValue: 0
      m_ManualQueueTargetFrame: 0
    m_Capture: {fileID: 11400000, guid: 05293ab353dc8a747a36ed129311686d, type: 2}
  - m_Name: Fist
    m_ToggleInput:
      m_InputSourceMode: 2
      m_InputActionPerformed:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 725934b7-db89-4d01-a944-8760f9586bcd
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionValue:
        m_Name: 
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: f2556394-ebee-4447-aab9-a0c752530e74
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_InputActionReferencePerformed: {fileID: 6469712917552426222, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_InputActionReferenceValue: {fileID: 6469712917552426222, guid: b72ab2a46d9094be38774d023beb4d34, type: 3}
      m_ObjectReferenceObject: {fileID: 0}
      m_ManualPerformed: 0
      m_ManualValue: 0
      m_ManualQueuePerformed: 0
      m_ManualQueueWasPerformedThisFrame: 0
      m_ManualQueueWasCompletedThisFrame: 0
      m_ManualQueueValue: 0
      m_ManualQueueTargetFrame: 0
    m_Capture: {fileID: 11400000, guid: 2a7c8ca0feac7cc44a5c225164ef311d, type: 2}
  m_RestingHandExpressionCapture: {fileID: 11400000, guid: 5be099e6e6012c244bb41881b6c0ea07, type: 2}
