%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4318511848014193775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4978570559794932741}
  - component: {fileID: 2418615375201463647}
  - component: {fileID: 8159150361180033376}
  - component: {fileID: 4256384458682596258}
  m_Layer: 0
  m_Name: Visuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4978570559794932741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318511848014193775}
  m_LocalRotation: {x: -0, y: -0, z: -0.000000008381902, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 4.826501, y: 4.826501, z: 4.826501}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4905619921565087965}
  m_Father: {fileID: 5013556786019211059}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &2418615375201463647
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318511848014193775}
  m_Mesh: {fileID: -2934717439318261032, guid: 30ab630b2b7264c1c85cca8ce81b06c3, type: 3}
--- !u!23 &8159150361180033376
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318511848014193775}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 76618f7490c40334fa7b685859587d2e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!135 &4256384458682596258
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318511848014193775}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.006500003
  m_Center: {x: 0, y: -0.002, z: 0}
--- !u!1 &8236831428665510606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4905619921565087965}
  - component: {fileID: 4129596266679649003}
  - component: {fileID: 7670699472714972690}
  - component: {fileID: 4477334520055318792}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4905619921565087965
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8236831428665510606}
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.0096, z: -0}
  m_LocalScale: {x: 0.41586, y: 1.3768293, z: 0.41586}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4978570559794932741}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4129596266679649003
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8236831428665510606}
  m_Mesh: {fileID: -5076798556035486163, guid: bf65382e5e6d14e7f8140e4204ce07e2, type: 3}
--- !u!23 &7670699472714972690
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8236831428665510606}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 76618f7490c40334fa7b685859587d2e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &4477334520055318792
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8236831428665510606}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.005500003
  m_Height: 0.0100000035
  m_Direction: 1
  m_Center: {x: 0, y: -0.001, z: 0}
--- !u!1 &8343167407413159200
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5013556786019211059}
  - component: {fileID: 8343167407413159202}
  - component: {fileID: 8343167407413159201}
  - component: {fileID: 8343167407413159203}
  m_Layer: 0
  m_Name: Arrow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5013556786019211059
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343167407413159200}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4978570559794932741}
  - {fileID: 8777580590156759232}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &8343167407413159202
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343167407413159200}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 1
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &8343167407413159201
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343167407413159200}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ad34abafad169848a38072baa96cdb2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_Colliders: []
  m_InteractionLayers:
    m_Bits: 1
  m_DistanceCalculationMode: 1
  m_SelectMode: 1
  m_FocusMode: 1
  m_CustomReticle: {fileID: 0}
  m_AllowGazeInteraction: 0
  m_AllowGazeSelect: 0
  m_OverrideGazeTimeToSelect: 0
  m_GazeTimeToSelect: 0.5
  m_OverrideTimeToAutoDeselectGaze: 0
  m_TimeToAutoDeselectGaze: 3
  m_AllowGazeAssistance: 0
  m_FirstHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstSelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastSelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstFocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastFocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_FocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_FocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_Activated:
    m_PersistentCalls:
      m_Calls: []
  m_Deactivated:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_StartingInteractionStrengthFilters: []
  m_AttachTransform: {fileID: 0}
  m_SecondaryAttachTransform: {fileID: 0}
  m_UseDynamicAttach: 1
  m_MatchAttachPosition: 1
  m_MatchAttachRotation: 1
  m_SnapToColliderVolume: 1
  m_ReinitializeDynamicAttachEverySingleGrab: 1
  m_AttachEaseInTime: 0.15
  m_MovementType: 2
  m_VelocityDamping: 1
  m_VelocityScale: 1
  m_AngularVelocityDamping: 1
  m_AngularVelocityScale: 1
  m_TrackPosition: 1
  m_SmoothPosition: 0
  m_SmoothPositionAmount: 8
  m_TightenPosition: 0.1
  m_TrackRotation: 1
  m_SmoothRotation: 0
  m_SmoothRotationAmount: 8
  m_TightenRotation: 0.1
  m_TrackScale: 1
  m_SmoothScale: 1
  m_SmoothScaleAmount: 8
  m_TightenScale: 0.1
  m_ThrowOnDetach: 0
  m_ThrowSmoothingDuration: 0.25
  m_ThrowSmoothingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_ThrowVelocityScale: 1.5
  m_ThrowAngularVelocityScale: 1
  m_ForceGravityOnDetach: 0
  m_RetainTransformParent: 1
  m_StartingSingleGrabTransformers: []
  m_StartingMultipleGrabTransformers: []
  m_AddDefaultGrabTransformers: 1
  m_FarAttachMode: 0
--- !u!114 &8343167407413159203
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343167407413159200}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0a1302d0d134fa8a2a5b3bf4aec3c20, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PermittedDisplacementAxes: 7
  m_ConstrainedAxisDisplacementMode: 1
  m_TwoHandedRotationMode: 1
  m_AllowOneHandedScaling: 1
  m_AllowTwoHandedScaling: 1
  m_OneHandedScaleSpeed: 0.5
  m_ThresholdMoveRatioForScale: 0.01
  m_ClampScaling: 1
  m_MinimumScaleRatio: 0.25
  m_MaximumScaleRatio: 3
  m_ScaleMultiplier: 0.25
--- !u!1 &8439317372374805806
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3848225726396102574}
  - component: {fileID: 5408859887124869817}
  - component: {fileID: 8874141616362956421}
  - component: {fileID: 2522668584211318101}
  m_Layer: 0
  m_Name: Material Affordance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3848225726396102574
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8439317372374805806}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8777580590156759232}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5408859887124869817
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8439317372374805806}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 7670699472714972690}
  m_MaterialIndex: 0
--- !u!114 &8874141616362956421
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8439317372374805806}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 5114438700417830185}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: 8b0b5aede76faac438e02d2a468f4805, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 5408859887124869817}
  m_ColorPropertyName: _RimColor
--- !u!114 &2522668584211318101
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8439317372374805806}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 629ea686265f47f082ba5732cffad1cf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 5114438700417830185}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: idle
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hovered
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hoveredPriority
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: selected
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: activated
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: focused
        animationStateStartValue: 0
        animationStateEndValue: 0
    m_Variable: {fileID: 11400000, guid: 795305341a8dbbd46ae54e9a01d6ea95, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 5408859887124869817}
  m_FloatPropertyName: _RimPower
--- !u!1001 &3542471709012220395
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5013556786019211059}
    m_Modifications:
    - target: {fileID: 3774509235512974894, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_Name
      value: Highlight Interaction Affordance
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7396278978564332023, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_Renderer
      value: 
      objectReference: {fileID: 8159150361180033376}
    - target: {fileID: 8634317094661461186, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_InteractableSource
      value: 
      objectReference: {fileID: 8343167407413159201}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
--- !u!114 &5114438700417830185 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 8634317094661461186, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
  m_PrefabInstance: {fileID: 3542471709012220395}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 49e0a5b5ff5540f5b14dd29d46faec22, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &8777580590156759232 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
  m_PrefabInstance: {fileID: 3542471709012220395}
  m_PrefabAsset: {fileID: 0}
