{"name": "Nyxion.AssetManager", "rootNamespace": "Nyxion.AssetManager", "references": ["Unity.Addressables", "Unity.ResourceManager", "glTFast"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.atteneder.gltfast", "expression": "6.0.0", "define": "GLTFAST_AVAILABLE"}], "noEngineReferences": false}