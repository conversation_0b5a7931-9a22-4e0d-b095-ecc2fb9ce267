#if GLTFAST_AVAILABLE
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using GLTFast;

namespace Nyxion.AssetManager.Providers
{
    /// <summary>
    /// Model provider for loading glTF/GLB files via glTFast
    /// </summary>
    public class GltfModelProvider : ModelProviderBase
    {
        private readonly Dictionary<GameObject, GltfImport> _loadedImports = new Dictionary<GameObject, GltfImport>();
        private readonly object _lock = new object();

        /// <summary>
        /// Checks if the address is a glTF/GLB URL
        /// </summary>
        public override bool CanHandle(string addressOrUrl)
        {
            if (string.IsNullOrEmpty(addressOrUrl))
                return false;

            // Check for HTTP/HTTPS URLs
            if (addressOrUrl.StartsWith("http://", StringComparison.OrdinalIgnoreCase) ||
                addressOrUrl.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
                return true;

            // Check for glTF/GLB file extensions
            if (addressOrUrl.EndsWith(".gltf", StringComparison.OrdinalIgnoreCase) ||
                addressOrUrl.EndsWith(".glb", StringComparison.OrdinalIgnoreCase))
                return true;

            return false;
        }

        /// <summary>
        /// Loads a glTF/GLB model from URL
        /// </summary>
        public override async Task<GameObject> LoadAsync(string addressOrUrl, Transform parent = null, CancellationToken cancellationToken = default)
        {
            if (!CanHandle(addressOrUrl))
            {
                var error = $"GltfModelProvider cannot handle: {addressOrUrl}";
                OnLoadFailed(addressOrUrl, error);
                return null;
            }

            try
            {
                Debug.Log($"Loading glTF model from URL: {addressOrUrl}");
                OnLoadProgressChanged(addressOrUrl, 0f);

                var gltfImport = new GltfImport();

                // Load the glTF
                var success = await gltfImport.Load(addressOrUrl);
                
                if (!success)
                {
                    var error = $"Failed to load glTF from {addressOrUrl}";
                    Debug.LogError(error);
                    OnLoadFailed(addressOrUrl, error);
                    gltfImport?.Dispose();
                    return null;
                }

                cancellationToken.ThrowIfCancellationRequested();
                OnLoadProgressChanged(addressOrUrl, 0.7f);

                // Instantiate the model
                var rootGameObject = new GameObject($"glTF_{System.IO.Path.GetFileNameWithoutExtension(addressOrUrl)}");
                if (parent != null)
                {
                    rootGameObject.transform.SetParent(parent, false);
                }

                success = await gltfImport.InstantiateMainSceneAsync(rootGameObject.transform);
                
                if (!success)
                {
                    var error = $"Failed to instantiate glTF scene from {addressOrUrl}";
                    Debug.LogError(error);
                    OnLoadFailed(addressOrUrl, error);
                    
                    if (rootGameObject != null)
                    {
                        if (Application.isPlaying)
                            UnityEngine.Object.Destroy(rootGameObject);
                        else
                            UnityEngine.Object.DestroyImmediate(rootGameObject);
                    }
                    
                    gltfImport?.Dispose();
                    return null;
                }

                cancellationToken.ThrowIfCancellationRequested();

                lock (_lock)
                {
                    _loadedImports[rootGameObject] = gltfImport;
                }

                OnLoadProgressChanged(addressOrUrl, 1f);
                OnLoadCompleted(addressOrUrl, rootGameObject);
                
                Debug.Log($"Successfully loaded glTF model: {addressOrUrl}");
                return rootGameObject;
            }
            catch (OperationCanceledException)
            {
                Debug.Log($"glTF load operation cancelled for: {addressOrUrl}");
                throw;
            }
            catch (Exception ex)
            {
                var error = $"Exception loading glTF {addressOrUrl}: {ex.Message}";
                Debug.LogError(error);
                OnLoadFailed(addressOrUrl, error);
                return null;
            }
        }

        /// <summary>
        /// Gets download size for glTF (not easily determinable without downloading)
        /// </summary>
        public override async Task<long> GetDownloadSizeAsync(string addressOrUrl, CancellationToken cancellationToken = default)
        {
            if (!CanHandle(addressOrUrl))
                return 0;

            try
            {
                // For HTTP URLs, we could try a HEAD request to get Content-Length
                // For now, return 0 as size is not easily determinable without downloading
                Debug.LogWarning($"Download size estimation not implemented for glTF URLs: {addressOrUrl}");
                return 0;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Exception getting download size for glTF {addressOrUrl}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Releases a loaded glTF model
        /// </summary>
        public override void Release(GameObject model)
        {
            if (model == null)
                return;

            lock (_lock)
            {
                if (_loadedImports.TryGetValue(model, out var gltfImport))
                {
                    _loadedImports.Remove(model);
                    gltfImport?.Dispose();
                    Debug.Log($"Released glTF import for: {model.name}");
                }
            }

            // Destroy the GameObject
            if (Application.isPlaying)
                UnityEngine.Object.Destroy(model);
            else
                UnityEngine.Object.DestroyImmediate(model);
        }

        /// <summary>
        /// Releases all loaded glTF models
        /// </summary>
        public void ReleaseAll()
        {
            lock (_lock)
            {
                foreach (var kvp in _loadedImports)
                {
                    kvp.Value?.Dispose();
                    
                    if (kvp.Key != null)
                    {
                        if (Application.isPlaying)
                            UnityEngine.Object.Destroy(kvp.Key);
                        else
                            UnityEngine.Object.DestroyImmediate(kvp.Key);
                    }
                }
                _loadedImports.Clear();
            }
            
            Debug.Log("Released all glTF imports");
        }
    }
}
#else
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;

namespace Nyxion.AssetManager.Providers
{
    /// <summary>
    /// Stub implementation when glTFast is not available
    /// </summary>
    public class GltfModelProvider : ModelProviderBase
    {
        public override bool CanHandle(string addressOrUrl) => false;

        public override Task<GameObject> LoadAsync(string addressOrUrl, Transform parent = null, CancellationToken cancellationToken = default)
        {
            Debug.LogWarning("glTFast package is not installed. Cannot load glTF/GLB files.");
            return Task.FromResult<GameObject>(null);
        }

        public override Task<long> GetDownloadSizeAsync(string addressOrUrl, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(0L);
        }

        public override void Release(GameObject model)
        {
            // Nothing to do
        }
    }
}
#endif
