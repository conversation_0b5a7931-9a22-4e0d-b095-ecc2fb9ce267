%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1323442585405899171
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 893315461643433812}
  - component: {fileID: 3755238779732181253}
  - component: {fileID: 3053154067257784704}
  - component: {fileID: 6278253758758756215}
  m_Layer: 0
  m_Name: LineVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &893315461643433812
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1323442585405899171}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5745700813747042508}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3755238779732181253
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1323442585405899171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 80e353695beb436ab39a90d9ecefaee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LineRenderer: {fileID: 3053154067257784704}
  m_CurveVisualObject: {fileID: 2442306273320644280}
  m_OverrideLineOrigin: 1
  m_LineOriginTransform: {fileID: 5745700813747042508}
  m_VisualPointCount: 20
  m_MaxVisualCurveDistance: 10
  m_RestingVisualLineLength: 0.25
  m_LineDynamicsMode: 1
  m_RetractDelay: 1
  m_RetractDuration: 1
  m_ExtendLineToEmptyHit: 0
  m_ExtensionRate: 10
  m_EndPointExpansionRate: 10
  m_ComputeMidPointWithComplexCurves: 0
  m_SnapToSelectedAttachIfAvailable: 1
  m_SnapToSnapVolumeIfAvailable: 1
  m_CurveStartOffset: 0.015
  m_CurveEndOffset: 0.005
  m_CustomizeLinePropertiesForState: 1
  m_LinePropertyAnimationSpeed: 8
  m_NoValidHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.25
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 1, b: 1, a: 0.5019608}
      key2: {r: 0, g: 0, b: 0, a: 0.2509804}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 193
      atime1: 8192
      atime2: 32768
      atime3: 55705
      atime4: 65342
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 2
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_UIHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.004
    m_EndWidth: 0.004
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 0.78431374, b: 0.5686275, a: 0.49019608}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 16384
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 32768
      atime3: 65535
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_UIPressHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 0.5686275, g: 0.78431374, b: 1, a: 0.627451}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0.78431374}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 32768
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 26214
      atime3: 42598
      atime4: 65535
      atime5: 65535
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 5
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_SelectHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 0.5686275, g: 0.78431374, b: 1, a: 0.627451}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0.78431374}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 32768
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 26214
      atime3: 42598
      atime4: 65535
      atime5: 65535
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 5
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.75
  m_HoverHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.25
    m_AdjustWidth: 1
    m_StarWidth: 0.004
    m_EndWidth: 0.004
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 0.78431374, b: 0.5686275, a: 0.49019608}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 16384
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 13878
      atime2: 32768
      atime3: 65535
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_RenderLineInWorldSpace: 1
  m_SwapMaterials: 0
  m_BaseLineMaterial: {fileID: 0}
  m_EmptyHitMaterial: {fileID: 0}
--- !u!120 &3053154067257784704
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1323442585405899171}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 1
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.012002945
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 8
    numCapVertices: 8
    alignment: 0
    textureMode: 0
    shadowBias: 0.5
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!210 &6278253758758756215
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1323442585405899171}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 30005
--- !u!1 &4804964734930210078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5745700813747042508}
  - component: {fileID: 2442306273320644280}
  - component: {fileID: 1722882099693224055}
  - component: {fileID: 7104419533170684624}
  - component: {fileID: 1255647619390271626}
  - component: {fileID: 3179295312718945089}
  m_Layer: 0
  m_Name: Left_NearFarInteractor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5745700813747042508
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4804964734930210078}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 893315461643433812}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2442306273320644280
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4804964734930210078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 25a07ef133a37d140a87cdf1f1c75fdf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 1
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 2491b664-3d4e-4f20-a7ae-ee1861d845f2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: fc42dacc-33eb-41ec-9c17-d242ac6b0c5b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -6131295136447488360, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_InputActionReferenceValue: {fileID: 6558622148059887818, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 77660b9e-6bbe-4740-b80f-1fea8d0f59e1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 94568f52-c27a-47fc-a190-5e3b17572929
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -5982496924579745919, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_InputActionReferenceValue: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_InteractionAttachController: {fileID: 1722882099693224055}
  m_EnableNearCasting: 1
  m_NearInteractionCaster: {fileID: 7104419533170684624}
  m_NearCasterSortingStrategy: 1
  m_SortNearTargetsAfterTargetFilter: 0
  m_EnableFarCasting: 1
  m_FarInteractionCaster: {fileID: 1255647619390271626}
  m_FarAttachMode: 1
  m_EnableUIInteraction: 1
  m_BlockUIOnInteractableSelection: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_UIPressInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: ded1ccb2-ff18-46c7-ade9-b80985fe2825
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d762660e-30e0-4a4d-8e2a-e6b553e03f11
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -6395602842196007441, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_InputActionReferenceValue: {fileID: 71106601250685021, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: ef5cc4a5-b968-432c-9ae7-45e494178db0
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!114 &1722882099693224055
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4804964734930210078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 792f6c7eaa1a4b82abf8351559ac97eb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransformToFollow: {fileID: 5745700813747042508}
  m_MotionStabilizationMode: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_SmoothOffset: 0
  m_SmoothingSpeed: 10
  m_UseDistanceBasedVelocityScaling: 1
  m_UseMomentum: 1
  m_MomentumDecayScale: 1.25
  m_MomentumDecayScaleFromInput: 5.5
  m_ZVelocityRampThreshold: 0.3
  m_PullVelocityBias: 1
  m_PushVelocityBias: 1.25
  m_MinAdditionalVelocityScalar: 0.05
  m_MaxAdditionalVelocityScalar: 1.5
  m_UseManipulationInput: 1
  m_ManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 9914e1e2-77ba-43ae-8266-fd33d44c8a24
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8811388872089202044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ManipulationXAxisMode: 1
  m_ManipulationYAxisMode: 2
  m_CombineManipulationAxes: 0
  m_ManipulationTranslateSpeed: 1
  m_ManipulationRotateSpeed: 180
  m_ManipulationRotateReferenceFrame: {fileID: 0}
--- !u!114 &7104419533170684624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4804964734930210078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 48139a683d3b4ac3a37cd5d24f71acf1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 5745700813747042508}
  m_EnableStabilization: 0
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 0}
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_PhysicsTriggerInteraction: 1
  m_CastRadius: 0.1
--- !u!114 &1255647619390271626
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4804964734930210078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef20135915079454985abea5a2ec8967, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 5745700813747042508}
  m_EnableStabilization: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 2442306273320644280}
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 2147483681
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_TargetNumCurveSegments: 1
  m_HitDetectionType: 2
  m_CastDistance: 10
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
--- !u!114 &3179295312718945089
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4804964734930210078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 2442306273320644280}
  m_HapticImpulsePlayer: {fileID: 0}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 0
