---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**Files**

Attach or link to *.gltf/*.glb files that trigger the bug. 

In addition, make sure to run those files through the [glTF Validator](https://github.khronos.org/glTF-Validator) first. If you encounter errors or warnings, try to make sure they are not responsible for the issue and file a bug report with the software that generated the glTF file as well.

> [!TIP]
> You have to ZIP archive them first in order for GitHub to accept the upload.

If your files are confidential:

- Try to create a similar, but intellectual-property-free glTF that reproduces the bug in the same way (so any community member can have a look)
- Otherwise, still create this issue and send the files (or a link to them) discretely via email

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Desktop (please complete the following information):**
 - glTFast version
 - Unity Editor version [e.g. 2021.2.1f1]
 - Render Pipeline and version [e.g. Universal Render Pipeline 12.0]
 - Platform: [e.g. Editor, Windows Player, iOS]
 
additionally (if significant for the bug):

 - Device: [e.g. iPhone6]
 - OS: [e.g. iOS8.1]
 - For WebGL: Browser [e.g. stock browser, safari]

**Additional context**
Add any other context about the problem here.
