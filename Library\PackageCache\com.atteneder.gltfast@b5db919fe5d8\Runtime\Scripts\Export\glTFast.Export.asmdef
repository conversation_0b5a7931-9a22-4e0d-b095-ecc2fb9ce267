{"name": "glTFast.Export", "rootNamespace": "GLTFast.Export", "references": ["glTFast", "Unity.Mathematics", "Unity.Burst", "Unity.RenderPipelines.Core.Runtime", "Unity.RenderPipelines.Universal.Runtime", "Unity.RenderPipelines.HighDefinition.Runtime", "Draco.Encode"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [{"name": "com.unity.render-pipelines.core", "expression": "17", "define": "RENDER_PIPELINES_CORE_17_OR_NEWER"}, {"name": "com.unity.render-pipelines.universal", "expression": "7.3.1", "define": "USING_URP"}, {"name": "com.unity.render-pipelines.universal", "expression": "12", "define": "USING_URP_12_OR_NEWER"}, {"name": "com.unity.render-pipelines.high-definition", "expression": "7.3.1", "define": "USING_HDRP"}, {"name": "com.unity.modules.imageconversion", "expression": "", "define": "UNITY_IMAGECONVERSION"}, {"name": "com.unity.cloud.draco", "expression": "5.0.0-pre.1", "define": "DRACO_UNITY"}, {"name": "com.unity.shadergraph", "expression": "", "define": "UNITY_SHADER_GRAPH"}], "noEngineReferences": false}