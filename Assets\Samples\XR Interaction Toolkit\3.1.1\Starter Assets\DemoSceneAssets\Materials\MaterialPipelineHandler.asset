%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7883133e628dff4a86f50c082f77055, type: 3}
  m_Name: MaterialPipelineHandler
  m_EditorClassIdentifier: 
  m_ShaderContainers:
  - material: {fileID: 2100000, guid: bbb56ac3cf3c61a46ab3887c0fdbda8f, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 842f1b88643f1bb458ba6243088e344e, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 830d28b607e09a2479e2005c2eb5c75e, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: fc226930e8fad4c499969204cecfbc05, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  m_AutoRefreshShaders: 1
