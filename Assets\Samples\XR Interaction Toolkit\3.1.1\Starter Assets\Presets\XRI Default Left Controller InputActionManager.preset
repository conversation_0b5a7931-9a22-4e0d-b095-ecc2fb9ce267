%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Left Controller InputActionManager
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: f9ac216f0eb04754b1d938aac6380b31, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RayInteractor
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_NearFarInteractor
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TeleportInteractor
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TeleportMode
    value: 
    objectReference: {fileID: 1263111715868034790, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TeleportModeCancel
    value: 
    objectReference: {fileID: 737890489006591557, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_Turn
    value: 
    objectReference: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SnapTurn
    value: 
    objectReference: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_Move
    value: 
    objectReference: {fileID: 6972639530819350904, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIScroll
    value: 
    objectReference: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SmoothMotionEnabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SmoothTurnEnabled
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_NearFarEnableTeleportDuringNearInteraction
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollingEnabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RayInteractorChanged.m_PersistentCalls.m_Calls.Array.size
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
