%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Snap Turn
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: e9f365cf844c03449bc8973eead2c3c1, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Mediator
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TransformationPriority
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TurnAmount
    value: 45
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DebounceTime
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableTurnLeftRight
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableTurnAround
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DelayTime
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Name
    value: Left Hand Snap Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Id
    value: 536e141d-ee23-4272-b0fd-3984d1655f02
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputAction.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_InputActionReference
    value: 
    objectReference: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_ManualValue.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnInput.m_ManualValue.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Name
    value: Right Hand Snap Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Id
    value: b17ca378-4740-48c7-abe1-7f35bce317e9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputAction.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_InputActionReference
    value: 
    objectReference: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_ManualValue.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnInput.m_ManualValue.y
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
