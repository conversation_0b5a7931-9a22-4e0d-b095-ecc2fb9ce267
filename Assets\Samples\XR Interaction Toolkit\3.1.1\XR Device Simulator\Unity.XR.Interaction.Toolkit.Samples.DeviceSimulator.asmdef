{"name": "Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator", "rootNamespace": "", "references": ["GUID:75469ad4d38634e559750d17036d5f7c", "GUID:dc960734dc080426fa6612f1c5fe95f3", "GUID:fe685ec1767f73d42b749ea8045bfe43", "GUID:ce522b6ed64c8be4c989a1d26d0e3275"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.xr.hands", "expression": "1.1.0", "define": "XR_HANDS_1_1_OR_NEWER"}, {"name": "Unity", "expression": "[2020.3.45,2021.1)", "define": "HAS_FIND_FIRST_OBJECT_BY_TYPE"}, {"name": "Unity", "expression": "[2021.3.18,2022.1.0)", "define": "HAS_FIND_FIRST_OBJECT_BY_TYPE"}, {"name": "Unity", "expression": "[2022.2.5,2022.3)", "define": "HAS_FIND_FIRST_OBJECT_BY_TYPE"}, {"name": "Unity", "expression": "2022.3.0", "define": "HAS_FIND_FIRST_OBJECT_BY_TYPE"}], "noEngineReferences": false}