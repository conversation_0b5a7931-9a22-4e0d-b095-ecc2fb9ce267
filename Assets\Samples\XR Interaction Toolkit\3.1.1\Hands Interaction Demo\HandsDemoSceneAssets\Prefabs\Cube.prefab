%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2468804214868727367
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8715892259302707255}
  - component: {fileID: 8468258526941819482}
  - component: {fileID: 2603633290551499573}
  - component: {fileID: 4579233057008077181}
  m_Layer: 0
  m_Name: Visuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8715892259302707255
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2468804214868727367}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9171031756790316709}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 116.218, y: -85.48, z: -18.765}
--- !u!33 &8468258526941819482
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2468804214868727367}
  m_Mesh: {fileID: 4068373912626513877, guid: 7cf3ac7bcd2e5471fb54d5f5da9aad56, type: 3}
--- !u!23 &2603633290551499573
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2468804214868727367}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 76618f7490c40334fa7b685859587d2e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4579233057008077181
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2468804214868727367}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.01, y: 0.01, z: 0.01}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &5268460675038366902
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9171031756790316709}
  - component: {fileID: 5268460675038366900}
  - component: {fileID: 5268460675038366903}
  - component: {fileID: 1988286665728152634}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9171031756790316709
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5268460675038366902}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8715892259302707255}
  - {fileID: 2591278628457874792}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5268460675038366900
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5268460675038366902}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 1
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5268460675038366903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5268460675038366902}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ad34abafad169848a38072baa96cdb2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_Colliders: []
  m_InteractionLayers:
    m_Bits: 1
  m_DistanceCalculationMode: 1
  m_SelectMode: 1
  m_FocusMode: 1
  m_CustomReticle: {fileID: 0}
  m_AllowGazeInteraction: 0
  m_AllowGazeSelect: 0
  m_OverrideGazeTimeToSelect: 0
  m_GazeTimeToSelect: 0.5
  m_OverrideTimeToAutoDeselectGaze: 0
  m_TimeToAutoDeselectGaze: 3
  m_AllowGazeAssistance: 0
  m_FirstHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstSelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastSelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstFocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastFocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_FocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_FocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_Activated:
    m_PersistentCalls:
      m_Calls: []
  m_Deactivated:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_StartingInteractionStrengthFilters: []
  m_AttachTransform: {fileID: 0}
  m_SecondaryAttachTransform: {fileID: 0}
  m_UseDynamicAttach: 1
  m_MatchAttachPosition: 1
  m_MatchAttachRotation: 1
  m_SnapToColliderVolume: 1
  m_ReinitializeDynamicAttachEverySingleGrab: 1
  m_AttachEaseInTime: 0.15
  m_MovementType: 0
  m_VelocityDamping: 1
  m_VelocityScale: 1
  m_AngularVelocityDamping: 1
  m_AngularVelocityScale: 1
  m_TrackPosition: 1
  m_SmoothPosition: 0
  m_SmoothPositionAmount: 8
  m_TightenPosition: 0.1
  m_TrackRotation: 1
  m_SmoothRotation: 0
  m_SmoothRotationAmount: 8
  m_TightenRotation: 0.1
  m_TrackScale: 1
  m_SmoothScale: 1
  m_SmoothScaleAmount: 8
  m_TightenScale: 0.1
  m_ThrowOnDetach: 1
  m_ThrowSmoothingDuration: 0.25
  m_ThrowSmoothingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_ThrowVelocityScale: 1.5
  m_ThrowAngularVelocityScale: 1
  m_ForceGravityOnDetach: 0
  m_RetainTransformParent: 1
  m_StartingSingleGrabTransformers: []
  m_StartingMultipleGrabTransformers: []
  m_AddDefaultGrabTransformers: 1
  m_FarAttachMode: 0
--- !u!114 &1988286665728152634
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5268460675038366902}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0a1302d0d134fa8a2a5b3bf4aec3c20, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PermittedDisplacementAxes: 7
  m_ConstrainedAxisDisplacementMode: 1
  m_TwoHandedRotationMode: 1
  m_AllowOneHandedScaling: 1
  m_AllowTwoHandedScaling: 1
  m_OneHandedScaleSpeed: 0.5
  m_ThresholdMoveRatioForScale: 0.01
  m_ClampScaling: 1
  m_MinimumScaleRatio: 0.25
  m_MaximumScaleRatio: 3
  m_ScaleMultiplier: 0.25
--- !u!1001 &7714466481515797571
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 9171031756790316709}
    m_Modifications:
    - target: {fileID: 3774509235512974894, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_Name
      value: Highlight Interaction Affordance
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7396278978564332023, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_Renderer
      value: 
      objectReference: {fileID: 2603633290551499573}
    - target: {fileID: 8634317094661461186, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_InteractableSource
      value: 
      objectReference: {fileID: 5268460675038366903}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
--- !u!4 &2591278628457874792 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
  m_PrefabInstance: {fileID: 7714466481515797571}
  m_PrefabAsset: {fileID: 0}
