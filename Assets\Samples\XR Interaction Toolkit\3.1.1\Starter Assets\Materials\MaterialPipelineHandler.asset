%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7883133e628dff4a86f50c082f77055, type: 3}
  m_Name: MaterialPipelineHandler
  m_EditorClassIdentifier: 
  m_ShaderContainers:
  - material: {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 9f12d299d16099343a3c5c0d7285822a, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: fd3c5d8fce991e04f9c11109dde95b3b, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 76618f7490c40334fa7b685859587d2e, type: 2}
    useSRPShaderName: 0
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: -6465566751694194690, guid: 0927d29e476ce5843b1f7d2a96943c51, type: 3}
    useBuiltinShaderName: 0
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 4800000, guid: b24c216c4acb0094c892a61dfbbb76b4, type: 3}
  - material: {fileID: 2100000, guid: f5ccd52dc494e054fbe7d7161dcabe25, type: 2}
    useSRPShaderName: 0
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: -6465566751694194690, guid: e19b5bb6cb8e91e43b1b5d81a069296f, type: 3}
    useBuiltinShaderName: 0
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 4800000, guid: b24c216c4acb0094c892a61dfbbb76b4, type: 3}
  - material: {fileID: 2100000, guid: be1e10ce8a6f8cc4fb08d11c7f722469, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: -6465566751694194690, guid: e19b5bb6cb8e91e43b1b5d81a069296f, type: 3}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 4800000, guid: b24c216c4acb0094c892a61dfbbb76b4, type: 3}
  m_AutoRefreshShaders: 1
