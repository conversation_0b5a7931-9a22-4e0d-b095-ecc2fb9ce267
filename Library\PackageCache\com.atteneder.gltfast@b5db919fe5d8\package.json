{"name": "com.atteneder.gltfast", "version": "6.13.0", "displayName": "glTFast", "description": "Use glTFast to import and export glTF 3D files efficiently at runtime or in the Editor", "unity": "2021.3", "keywords": ["mesh", "gltf", "asset", "format", "draco", "ktx", "basis", "universal", "basisu"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://pixel.engineer"}, "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.mathematics": "1.2.6", "com.unity.burst": "1.8.4", "com.unity.collections": "1.2.4"}, "unityRelease": "46f1", "_fingerprint": "b5db919fe5d84158e742524f1f7f2e32020b0141"}