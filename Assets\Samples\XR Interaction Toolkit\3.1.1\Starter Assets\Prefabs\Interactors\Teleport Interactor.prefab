%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2761784063978902507
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2761784063978902506}
  - component: {fileID: 2761784063978902503}
  - component: {fileID: 2761784063978902504}
  - component: {fileID: 2761784063978902505}
  - component: {fileID: 7708679388415899527}
  - component: {fileID: 3616344554909481683}
  m_Layer: 0
  m_Name: Teleport Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2761784063978902506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761784063978902507}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2761784063978902503
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761784063978902507}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 2147483648
  m_Handedness: 0
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 0
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 36843f28-4fd5-4729-b5a6-afe92ef11597
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 1a51c331-470d-4462-b8e1-2522a24bd40c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 0ace7244-e61f-4e60-8d0b-2ef8c3ae51af
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: bdf06a24-21b3-4f27-a8a3-72086e6c7f00
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 0
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 0
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 0
  m_LineType: 1
  m_BlendVisualLinePoints: 1
  m_MaxRaycastDistance: 30
  m_RayOriginTransform: {fileID: 0}
  m_ReferenceFrame: {fileID: 0}
  m_Velocity: 10
  m_Acceleration: 9.8
  m_AdditionalGroundHeight: 0.1
  m_AdditionalFlightTime: 0.5
  m_EndPointDistance: 30
  m_EndPointHeight: -10
  m_ControlPointDistance: 10
  m_ControlPointHeight: 5
  m_SampleFrequency: 50
  m_HitDetectionType: 0
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 2147483681
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_HitClosestOnly: 1
  m_HoverToSelect: 0
  m_HoverTimeToSelect: 0.5
  m_AutoDeselect: 0
  m_TimeToAutoDeselect: 1
  m_EnableUIInteraction: 0
  m_BlockUIOnInteractableSelection: 1
  m_ManipulateAttachTransform: 1
  m_UseForceGrab: 0
  m_RotateSpeed: 180
  m_TranslateSpeed: 0
  m_RotateReferenceFrame: {fileID: 0}
  m_RotateMode: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_EnableARRaycasting: 0
  m_OccludeARHitsWith3DObjects: 0
  m_OccludeARHitsWith2DObjects: 0
  m_ScaleMode: 0
  m_UIPressInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 54867c8e-3650-4605-a53c-ee8ffb351dcf
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 257d8673-0295-4ff5-b278-e63d20cd918b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 8c6b1aac-a242-4bf4-a5b3-bfad6e83b638
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TranslateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Translate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 0f9fd0ee-650d-41a6-ab30-2a036c425c21
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RotateManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Rotate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: cadca2d2-f642-4efc-a222-c1827be3e896
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DirectionalManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Directional Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 472cbca3-7add-47a9-a5fc-73d3d10107aa
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleToggleInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 692a9304-a2fd-4dbd-9e2f-2fb4b6154f1c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Scale Toggle Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d0cf082b-f2d7-4100-b069-651cf2820425
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ScaleOverTimeInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Over Time
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 6f835f0d-f1c2-461c-b8bc-edc587e89149
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleDistanceDeltaInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Distance Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 9140e1d5-f197-46d4-88c2-a02441edeac5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!120 &2761784063978902504
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761784063978902507}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.02
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    shadowBias: 0.5
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!114 &2761784063978902505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761784063978902507}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e988983f96fe1dd48800bcdfc82f23e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LineWidth: 0.02
  m_OverrideInteractorLineLength: 0
  m_LineLength: 10
  m_AutoAdjustLineLength: 0
  m_MinLineLength: 0.02
  m_UseDistanceToHitAsMaxLineLength: 1
  m_LineRetractionDelay: 0.5
  m_LineLengthChangeSpeed: 12
  m_WidthCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_SetLineColorGradient: 1
  m_ValidColorGradient:
    serializedVersion: 2
    key0: {r: 0.1254902, g: 0.5882353, b: 0.9529412, a: 0.5882353}
    key1: {r: 0.1254902, g: 0.5882353, b: 0.9529412, a: 0.5882353}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_InvalidColorGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0, b: 0, a: 0.5882353}
    key1: {r: 1, g: 0, b: 0, a: 0.5882353}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_BlockedColorGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    key1: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_TreatSelectionAsValidState: 0
  m_SmoothMovement: 0
  m_FollowTightness: 10
  m_SnapThresholdDistance: 10
  m_Reticle: {fileID: 8748868027195207512, guid: 893219773891c784ab469a39151879b4, type: 3}
  m_BlockedReticle: {fileID: 3177232254315139758, guid: a3fde713df4d99042a0403c4be9eea32, type: 3}
  m_StopLineAtFirstRaycastHit: 1
  m_StopLineAtSelection: 0
  m_SnapEndpointIfAvailable: 1
  m_LineBendRatio: 0.5
  m_OverrideInteractorLineOrigin: 1
  m_LineOriginTransform: {fileID: 0}
  m_LineOriginOffset: 0
--- !u!210 &7708679388415899527
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761784063978902507}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 30005
--- !u!114 &3616344554909481683
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761784063978902507}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 2761784063978902503}
  m_HapticImpulsePlayer: {fileID: 0}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 1
