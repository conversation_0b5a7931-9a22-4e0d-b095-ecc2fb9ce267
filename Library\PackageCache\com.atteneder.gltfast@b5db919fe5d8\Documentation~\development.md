# Development

> [!CAUTION]
> To do meaningful development you have to [switch to the Unity fork](./UpgradeGuides#transition-to-unity-gltfast), as this repository/branch does not contain the tools, test and projects required for development. See [Download Sources](./sources.md#download-sources) to learn where to obtain said fork.

Here's a guide to getting started with development, testing your modifications and making a contribution to *glTFast*.

As prerequisites you'll need to [install](installation.md) a version of Unity&reg; that is [compatible with *glTFast*](features.md#unity-version-support).

- [Download Sources](sources.md#download-sources)
- [Test Projects](test-project-setup.md)
  - [Provided Projects](test-project-setup.md#test-projects)
  - [Custom Project](test-project-setup.md#setup-a-custom-project)
- [Load glTF files](test-open-file.md)
- [Run Tests](tests.md)
- [Contribute](contributing.md)

## Trademarks

*Unity&reg;* is a registered trademark of [Unity Technologies][unity].

[unity]: https://unity.com
