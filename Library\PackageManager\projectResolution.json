{"context": {"projectPath": "C:/Users/<USER>/OneDrive/Documents/Nyxion/House_VR/Packages", "unityVersion": "6000.0.47f1"}, "inputs": ["C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Packages\\manifest.json", "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Packages\\packages-lock.json", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.atteneder.gltfast@https://github.com/atteneder/glTFast.git": {"name": "com.atteneder.gltfast", "displayName": "glTFast", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.atteneder.gltfast@b5db919fe5d8", "fingerprint": "b5db919fe5d84158e742524f1f7f2e32020b0141", "editorCompatibility": "2021.3.46f1", "version": "6.13.0", "source": "git", "testable": false}, "com.unity.addressables@2.3.1": {"name": "com.unity.addressables", "displayName": "Addressables", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.addressables@5d870284b3d9", "fingerprint": "5d870284b3d9ea49a95c391511d346cef111ef0e", "editorCompatibility": "2023.1.0a1", "version": "2.3.1", "source": "registry", "testable": false}, "com.unity.collab-proxy@2.9.1": {"name": "com.unity.collab-proxy", "displayName": "Unity Version Control", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.collab-proxy@8a82222c5449", "fingerprint": "8a82222c5449ac329b4bb6db589ab2f58c446f94", "editorCompatibility": "2021.3.0f1", "version": "2.9.1", "source": "registry", "testable": false}, "com.unity.feature.development@1.0.2": {"name": "com.unity.feature.development", "displayName": "Engineering", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.feature.development@767aadbc6eb7", "fingerprint": "767aadbc6eb72681a4ca807c8fa248e0230a0cef", "version": "1.0.2", "source": "builtin", "testable": false}, "com.unity.inputsystem@1.14.0": {"name": "com.unity.inputsystem", "displayName": "Input System", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7", "fingerprint": "7fe8299111a78212d8968229ab41a82e4991ba25", "editorCompatibility": "2021.3.0a1", "version": "1.14.0", "source": "registry", "testable": false}, "com.unity.learn.iet-framework@4.0.4": {"name": "com.unity.learn.iet-framework", "displayName": "Tutorial Framework", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.learn.iet-framework@104e397c56f7", "fingerprint": "104e397c56f7e54a6b20dd4ed443ecc9e65a9472", "editorCompatibility": "2021.3.0a1", "version": "4.0.4", "source": "registry", "testable": false}, "com.unity.multiplayer.center@1.0.0": {"name": "com.unity.multiplayer.center", "displayName": "Multiplayer Center", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546", "fingerprint": "f3fb577b3546594b97b8cc34307cd621f60f1c73", "editorCompatibility": "6000.0.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal@17.0.4": {"name": "com.unity.render-pipelines.universal", "displayName": "Universal RP", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a", "fingerprint": "39bda2df468a5acf495ecd0d959095e2a0b5c56d", "editorCompatibility": "6000.0.0a1", "version": "17.0.4", "source": "builtin", "testable": false}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry", "testable": false}, "com.unity.xr.hands@1.5.0": {"name": "com.unity.xr.hands", "displayName": "XR Hands", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.xr.hands@b137b9cef9d8", "fingerprint": "b137b9cef9d8ac6f2f21023d4def8cc316ae4dbc", "editorCompatibility": "2021.3.0a1", "version": "1.5.0", "source": "registry", "testable": false}, "com.unity.xr.interaction.toolkit@3.1.1": {"name": "com.unity.xr.interaction.toolkit", "displayName": "XR Interaction Toolkit", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.xr.interaction.toolkit@9b07900cb163", "fingerprint": "9b07900cb16350b70554da71cbe8466cfe0a8686", "editorCompatibility": "2021.3.0a1", "version": "3.1.1", "source": "registry", "testable": false}, "com.unity.xr.management@4.5.1": {"name": "com.unity.xr.management", "displayName": "XR Plugin Management", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.xr.management@20be87dea580", "fingerprint": "20be87dea58031bdc9e932e7e7cbe7d270db0e4a", "editorCompatibility": "2020.3.15f1", "version": "4.5.1", "source": "registry", "testable": false}, "com.unity.xr.openxr@1.14.1": {"name": "com.unity.xr.openxr", "displayName": "OpenXR Plugin", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.xr.openxr@db4bbb5cd412", "fingerprint": "db4bbb5cd412ddbe352755d176a472ce5a002505", "editorCompatibility": "2021.3.0a1", "version": "1.14.1", "source": "registry", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.xr.core-utils@2.5.2": {"name": "com.unity.xr.core-utils", "displayName": "XR Core Utilities", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.xr.core-utils@5b282bc7378d", "fingerprint": "5b282bc7378d257fcefbecad81c641874f85d033", "editorCompatibility": "2021.3.0f1", "version": "2.5.2", "source": "registry", "testable": false}, "com.unity.xr.legacyinputhelpers@2.1.12": {"name": "com.unity.xr.legacyinputhelpers", "displayName": "XR Legacy Input Helpers", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.xr.legacyinputhelpers@b7579e86f3b4", "fingerprint": "b7579e86f3b456c909fda6b875fba7ab1eeb1322", "editorCompatibility": "2019.4.0a1", "version": "2.1.12", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.ugui@f3fac7af1578", "fingerprint": "f3fac7af157843ea3b769ef7c6aa56de52618aeb", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.render-pipelines.core@17.0.4": {"name": "com.unity.render-pipelines.core", "displayName": "Core RP Library", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6", "fingerprint": "b1fa9fba9fd6ee318b59d862b2210cde3e167493", "editorCompatibility": "6000.0.0a1", "version": "17.0.4", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.0.4": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce", "fingerprint": "5c8f96f445ce3b641a34169a1f583821ec0021f0", "editorCompatibility": "6000.0.0a1", "version": "17.0.4", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal-config@17.0.3": {"name": "com.unity.render-pipelines.universal-config", "displayName": "Universal RP Config", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.render-pipelines.universal-config@a14cb52003ef", "fingerprint": "a14cb52003ef26456a831f608d213b6940a018ff", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.editorcoroutines@1.0.0": {"name": "com.unity.editorcoroutines", "displayName": "Editor Coroutines", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.editorcoroutines@7d48783e7b8c", "fingerprint": "7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca", "editorCompatibility": "2018.1.0a1", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.settings-manager@2.1.0": {"name": "com.unity.settings-manager", "displayName": "Settings Manager", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.settings-manager@41738c275190", "fingerprint": "41738c27519039c335849eb78949382f4d7a3544", "editorCompatibility": "2022.3.0a1", "version": "2.1.0", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.ide.rider@3.0.35": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.ide.rider@7d01a2258732", "fingerprint": "7d01a2258732f17d1f569657792eaba45b9633d4", "editorCompatibility": "2019.4.6f1", "version": "3.0.35", "source": "registry", "testable": false}, "com.unity.performance.profile-analyzer@1.2.3": {"name": "com.unity.performance.profile-analyzer", "displayName": "Profile Analyzer", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.performance.profile-analyzer@a68e7bc84997", "fingerprint": "a68e7bc849973d943853204178d08a2bc7656ffe", "editorCompatibility": "2020.3.0a1", "version": "1.2.3", "source": "registry", "testable": false}, "com.unity.test-framework@1.5.1": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.test-framework@388540dd9ce6", "fingerprint": "388540dd9ce60b7f4463a23bfdf0d2dafa9a6f3e", "editorCompatibility": "2022.3.0a1", "version": "1.5.1", "source": "builtin", "testable": false}, "com.unity.testtools.codecoverage@1.2.6": {"name": "com.unity.testtools.codecoverage", "displayName": "Code Coverage", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39", "fingerprint": "205a02cbcb39584f20b51c49b853047aceb3a3a7", "editorCompatibility": "2019.3.0a1", "version": "1.2.6", "source": "registry", "testable": false}, "com.unity.profiling.core@1.0.2": {"name": "com.unity.profiling.core", "displayName": "Unity Profiling Core API", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.profiling.core@aac7b93912bc", "fingerprint": "aac7b93912bc5df5fe06b04ff1b758493cdc2346", "editorCompatibility": "2020.1.0a1", "version": "1.0.2", "source": "registry", "testable": false}, "com.unity.scriptablebuildpipeline@2.1.5": {"name": "com.unity.scriptablebuildpipeline", "displayName": "Scriptable Build Pipeline", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.scriptablebuildpipeline@8cf66f134cb3", "fingerprint": "8cf66f134cb3f8a955d489173bfd88b3df4f8bf0", "editorCompatibility": "2019.4.0a1", "version": "2.1.5", "source": "registry", "testable": false}, "com.unity.burst@1.8.21": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.burst@59eb6f11d242", "fingerprint": "59eb6f11d2422f95682320d9daa3e79fdb076744", "editorCompatibility": "2020.3.0a1", "version": "1.8.21", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.searcher@4.9.3": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.searcher@1e17ce91558d", "fingerprint": "1e17ce91558d1d9127554adc03d275f39a7466a2", "editorCompatibility": "2019.1.0a1", "version": "4.9.3", "source": "registry", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.rendering.light-transport@307bc27a498f", "fingerprint": "307bc27a498fc9cd409bbd426c85d8dc7f140bc1", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.ext.nunit@031a54704bff", "fingerprint": "031a54704bffe39e6a0324909f8eaa4565bdebf2", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "builtin", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.test-framework.performance@3.1.0": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "C:\\Users\\<USER>\\OneDrive\\Documents\\Nyxion\\House_VR\\Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed", "fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50", "editorCompatibility": "2020.3.0a1", "version": "3.1.0", "source": "registry", "testable": false}}}