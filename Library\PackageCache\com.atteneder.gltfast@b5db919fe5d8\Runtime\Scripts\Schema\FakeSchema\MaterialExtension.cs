// SPDX-FileCopyrightText: 2023 Unity Technologies and the glTFast authors
// SPDX-License-Identifier: Apache-2.0

namespace GLTFast.FakeSchema
{

    [System.Serializable]
    class MaterialExtension
    {
        // ReSharper disable InconsistentNaming
        public string KHR_materials_pbrSpecularGlossiness;
        public string KHR_materials_unlit;
        public string KHR_materials_transmission;
        public string KHR_materials_clearcoat;
        public string KHR_materials_sheen;
        public string KHR_materials_ior;
        public string KHR_materials_specular;
        // ReSharper restore InconsistentNaming
    }
}
